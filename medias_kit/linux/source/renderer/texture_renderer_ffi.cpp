#include "renderer/texture_renderer_ffi.h"

#include "medias_kit_plugin_private.hpp"
#include "renderer/texture_renderer.hpp"
#include "utils/logger.hpp"

TextureRendererRef
textureRendererCreate(const char *pluginId) {
    try {
        MK::TextureRenderer *renderer = nullptr;
        MK::findPlugin(pluginId, [&](const std::string pluginId, const MediasKitPlugin &plugin) {
            renderer = new MK::TextureRenderer(plugin);
            if (!renderer->init()) {
                delete renderer;
                renderer = nullptr;
            }
        });
        return renderer;
    } catch (const std::exception &e) {
        MK::LOG_E("Failed to create texture renderer: {}", e.what());
        return nullptr;
    }
}

void
textureRendererDestroy(TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    delete rendererPtr;
}

void
textureRendererRenderRgbaFrame(
    TextureRendererRef renderer,
    const uint8_t *const frame,
    long frameLen,
    int64_t width,
    int64_t height
) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    rendererPtr->renderRgbaFrame(frame, frameLen, width, height);
}

int64_t
textureRendererGetTextureId(TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    return rendererPtr->getTexture();
}
