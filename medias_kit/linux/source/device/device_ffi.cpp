#include "device/device_ffi.h"

#include <atomic>
#include <cassert>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <flutter_linux/flutter_linux.h>
#include <functional>
#include <iomanip>
#include <libudev.h>
#include <linux/videodev2.h>
#include <map>
#include <memory>
#include <pulse/context.h>
#include <pulse/def.h>
#include <pulse/introspect.h>
#include <pulse/operation.h>
#include <pulse/subscribe.h>
#include <pulse/thread-mainloop.h>
#include <pulse/volume.h>
#include <string>
#include <sys/ioctl.h>
#include <sys/utsname.h>
#include <thread>
#include <tuple>
#include <unistd.h>
#include <vector>

#include "LibMWCapture/MWCapture.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

AudioDeviceRef
audioDeviceCreate(
    const bool isDefault,
    const uint32_t index,
    const char *const name,
    const char *const description,
    const int volume,
    const int channels,
    const int mute
) {
    auto device = new AudioDevice();
    device->isDefault = isDefault;
    device->index = index;
    if (name) device->name = strdup(name);
    if (description) device->description = strdup(description);
    device->volume = volume;
    device->channels = channels;
    device->mute = mute;
    return device;
}

AudioDeviceRef
audioDeviceCopy(const AudioDeviceRef device) {
    return audioDeviceCreate(
        device->isDefault,
        device->index,
        device->name,
        device->description,
        device->volume,
        device->channels,
        device->mute
    );
}

void
audioDeviceDestroy(const AudioDeviceRef device) {
    if (device->name) free(device->name);
    if (device->description) free(device->description);
    delete device;
}

VideoCaptureDeviceRef
videoCaptureDeviceCreate(
    const int index,
    const char *const path,
    const char *const name,
    const bool isUsbExtend
) {
    auto device = new VideoCaptureDevice();
    device->index = index;
    if (path) device->path = strdup(path);
    if (name) device->name = strdup(name);
    device->isUsbExtend = isUsbExtend;
    return device;
}

VideoCaptureDeviceRef
videoCaptureDeviceCopy(const VideoCaptureDeviceRef device) {
    return videoCaptureDeviceCreate(
        device->index,
        device->path,
        device->name,
        device->isUsbExtend
    );
}

void
videoCaptureDeviceDestroy(const VideoCaptureDeviceRef device) {
    if (device->path) free(device->path);
    if (device->name) free(device->name);
    delete device;
}

HostDeviceSpaceInfoRef
hostDeviceSpaceInfoCreate(
    const uint64_t capacity,
    const uint64_t free,
    const uint64_t available
) {
    auto info = new HostDeviceSpaceInfo();
    info->capacity = capacity;
    info->free = free;
    info->available = available;
    return info;
}

HostDeviceSpaceInfoRef
hostDeviceSpaceInfoCopy(const HostDeviceSpaceInfoRef info) {
    return hostDeviceSpaceInfoCreate(
        info->capacity,
        info->free,
        info->available
    );
}

void
hostDeviceSpaceInfoDestroy(const HostDeviceSpaceInfoRef info) {
    delete info;
}

const char *
getHostDevicePlatform() {
    static utsname unameData;
    if (strlen(unameData.version) == 0) {
        auto ret = uname(&unameData);
        if (ret != 0) {
            MK::LOG_E("{}", "uname failure!");
            return nullptr;
        }
    }
    return unameData.version;
}

HostDeviceSpaceInfoRef
getHostDeviceSpaceInfo(const char *const path) {
    assert(path);
    std::error_code errCode;
    auto spaceInfo = std::filesystem::space(path, errCode);
    if (errCode) {
        MK::LOG_E("{}", errCode.message());
        return nullptr;
    }
    return hostDeviceSpaceInfoCreate(
        spaceInfo.capacity,
        spaceInfo.free,
        spaceInfo.available
    );
}

pa_threaded_mainloop *mainloop = nullptr;
pa_context *context = nullptr;
std::map<const std::string, AudioDeviceRef> sinkDevices;
std::map<const std::string, AudioDeviceRef> sourceDevices;
std::string defaultSinkName;
std::string defaultSourceName;

void
sinkInfoCallback(
    pa_context *context,
    const pa_sink_info *sinkInfo,
    int eol,
    void *userdata
) {
    if (eol) {
        auto devices = (std::vector<AudioDeviceRef> *)vectorCreate();
        for (auto sinkDevice : sinkDevices) {
            devices->push_back(sinkDevice.second);
        }
        sinkDevices.clear();

        MK::Utils::sendNativeMessage(
            nativeMessageCreate(
                NativeEvent_audioSinkDeviceChanged,
                {.as_ptr = devices},
                0,
                [](void *ptr) {
                    auto devices = (std::vector<AudioDeviceRef> *)ptr;
                    auto newDevices = (std::vector<AudioDeviceRef> *)vectorCreate();
                    for (auto device : *devices) {
                        newDevices->push_back(audioDeviceCopy(device));
                    }
                    return (void *)newDevices;
                },
                [](void *ptr) {
                    vectorDestroy(ptr, (FreeElement)audioDeviceDestroy);
                }
            )
        );
        MK::LOG_I("{}", "Synced audio sinks!");
        return;
    }
    auto device = audioDeviceCreate(
        defaultSinkName == sinkInfo->name,
        sinkInfo->index,
        sinkInfo->name,
        sinkInfo->description,
        sinkInfo->volume.values[0] * 100 / PA_VOLUME_NORM,
        sinkInfo->volume.channels,
        sinkInfo->mute
    );
    sinkDevices[sinkInfo->name] = device;
}

void
sourceInfoCallback(
    pa_context *context,
    const pa_source_info *sourceInfo,
    int eol,
    void *userdata
) {
    if (eol) {
        auto devices = (std::vector<AudioDeviceRef> *)vectorCreate();
        for (auto sourceDevice : sourceDevices) {
            devices->push_back(sourceDevice.second);
        }
        sourceDevices.clear();

        MK::Utils::sendNativeMessage(
            nativeMessageCreate(
                NativeEvent_audioSourceDeviceChanged,
                {.as_ptr = devices},
                0,
                [](void *ptr) {
                    auto devices = (std::vector<AudioDeviceRef> *)ptr;
                    auto newDevices = (std::vector<AudioDeviceRef> *)vectorCreate();
                    for (auto device : *devices) {
                        newDevices->push_back(audioDeviceCopy(device));
                    }
                    return (void *)newDevices;
                },
                [](void *ptr) {
                    vectorDestroy(ptr, (FreeElement)audioDeviceDestroy);
                }
            )
        );
        MK::LOG_I("{}", "Synced audio sources!");
        return;
    }

    if (!sourceInfo->active_port) return;
    if (strcmp("analog-input-rear-mic", sourceInfo->active_port->name) == 0 &&
        PA_PORT_AVAILABLE_YES != sourceInfo->active_port->available)
        return;

    auto device = audioDeviceCreate(
        defaultSourceName == sourceInfo->name,
        sourceInfo->index,
        sourceInfo->name,
        sourceInfo->description,
        sourceInfo->volume.values[0] * 100 / PA_VOLUME_NORM,
        sourceInfo->volume.channels,
        sourceInfo->mute
    );
    sourceDevices[sourceInfo->name] = device;
}

void
serverInfoCallback(
    pa_context *context,
    const pa_server_info *info,
    void *userdata
) {
    int *facility = static_cast<int *>(userdata);
    defaultSinkName = std::string(info->default_sink_name);
    defaultSourceName = std::string(info->default_source_name);

    if (PA_SUBSCRIPTION_EVENT_SINK == *facility) {
        sinkDevices.clear();
        auto sinkOperation =
            pa_context_get_sink_info_list(context, sinkInfoCallback, userdata);
        if (sinkOperation) pa_operation_unref(sinkOperation);
    } else if (PA_SUBSCRIPTION_EVENT_SOURCE == *facility) {
        sourceDevices.clear();
        auto sourceOperation = pa_context_get_source_info_list(
            context, sourceInfoCallback, userdata
        );
        if (sourceOperation) pa_operation_unref(sourceOperation);
    } else {
        sinkDevices.clear();
        auto sinkOperation =
            pa_context_get_sink_info_list(context, sinkInfoCallback, userdata);
        if (sinkOperation) pa_operation_unref(sinkOperation);

        sourceDevices.clear();
        auto sourceOperation = pa_context_get_source_info_list(
            context, sourceInfoCallback, userdata
        );
        if (sourceOperation) pa_operation_unref(sourceOperation);
    }
    delete facility;
}

void
cardInfoCallback(
    pa_context *context,
    const pa_card_info *info,
    int eol,
    void *userdata
) {
    if (eol) {
        return;
    }
}

void
setupSinkCallback(pa_context *context, int success, void *userdata) {}

void
setupSourceCallback(pa_context *context, int success, void *userdata) {}

void
subscribeContextCallback(pa_context *context, int success, void *userdata) {}

void
stateCallback(pa_context *context, void *userdata) {
    pa_context_state_t state = pa_context_get_state(context);
    switch (state) {
    case PA_CONTEXT_READY: {
        auto facility = new int(-1);

        auto serverOperation =
            pa_context_get_server_info(context, serverInfoCallback, facility);
        if (serverOperation) pa_operation_unref(serverOperation);

        auto mask = pa_subscription_mask_t(
            PA_SUBSCRIPTION_MASK_SINK | PA_SUBSCRIPTION_MASK_SOURCE |
            PA_SUBSCRIPTION_MASK_CARD
        );
        auto subscribeOperation = pa_context_subscribe(
            context, mask, subscribeContextCallback, userdata
        );
        if (subscribeOperation) pa_operation_unref(subscribeOperation);
    } break;
    case PA_CONTEXT_FAILED:
    case PA_CONTEXT_TERMINATED:
        MK::LOG_E("{}", "PulseAudio context failed or terminated!");
        break;
    default:
        break;
    }
}

void
subscribeCallback(
    pa_context *context,
    pa_subscription_event_type_t type,
    uint32_t idx,
    void *userdata
) {
    auto operation = type & PA_SUBSCRIPTION_EVENT_TYPE_MASK;

    auto facility = new int();
    *facility = type & PA_SUBSCRIPTION_EVENT_FACILITY_MASK;

    // if (PA_SUBSCRIPTION_EVENT_CARD == facility) {
    //     pa_context_get_card_info_by_index(context, idx, cardInfoCallback, userdata);
    // }

    if (PA_SUBSCRIPTION_EVENT_CHANGE == operation ||
        PA_SUBSCRIPTION_EVENT_NEW == operation ||
        PA_SUBSCRIPTION_EVENT_REMOVE == operation) {
        auto op =
            pa_context_get_server_info(context, serverInfoCallback, facility);
        if (op) pa_operation_unref(op);
    }
}

std::atomic_bool _isListeningAudioDevices = false;
void
startListenAudioDevices() {
    if (_isListeningAudioDevices) {
        stateCallback(context, nullptr);
        return;
    }
    _isListeningAudioDevices = true;
    std::thread([]() {
        mainloop = pa_threaded_mainloop_new();
        auto *mainloop_api = pa_threaded_mainloop_get_api(mainloop);
        context = pa_context_new(mainloop_api, "surgsmart-audio-device-monitor");

        pa_context_set_state_callback(context, stateCallback, nullptr);
        pa_context_set_subscribe_callback(context, subscribeCallback, nullptr);

        pa_threaded_mainloop_start(mainloop);
        pa_threaded_mainloop_lock(mainloop);
        pa_context_connect(context, nullptr, PA_CONTEXT_NOFLAGS, nullptr);
        pa_threaded_mainloop_unlock(mainloop);
        while (true) {
            pa_threaded_mainloop_lock(mainloop);
            pa_context_state_t state = pa_context_get_state(context);
            pa_threaded_mainloop_unlock(mainloop);
            if (PA_CONTEXT_FAILED == state || PA_CONTEXT_TERMINATED == state) {
                break;
            }
            pa_threaded_mainloop_wait(mainloop);
        }

        pa_threaded_mainloop_lock(mainloop);
        pa_context_disconnect(context);
        pa_context_unref(context);
        pa_threaded_mainloop_unlock(mainloop);

        pa_threaded_mainloop_stop(mainloop);
        pa_threaded_mainloop_free(mainloop);
    }).detach();
}

void
setAudioSourceVolume(const AudioDeviceRef device, const int volume) {
    auto audioDevice = audioDeviceCopy(device);
    audioDevice->volume = volume;
    std::thread(
        [](AudioDeviceRef audioDevice) {
            pa_threaded_mainloop_lock(mainloop);
            pa_threaded_mainloop_once_unlocked(
                mainloop,
                [](pa_threaded_mainloop *mainloop, void *userdata) {
                    auto audioDevice = static_cast<AudioDeviceRef>(userdata);

                    auto percent = audioDevice->volume / 100.0;
                    pa_cvolume newVolume;
                    pa_cvolume_reset(&newVolume, audioDevice->channels);
                    pa_cvolume_set(
                        &newVolume,
                        audioDevice->channels,
                        PA_VOLUME_NORM * percent
                    );

                    auto muteOperation = pa_context_set_source_mute_by_name(
                        context,
                        audioDevice->name,
                        audioDevice->volume == 0,
                        setupSourceCallback,
                        nullptr
                    );
                    if (muteOperation) pa_operation_unref(muteOperation);

                    auto volumeOperation = pa_context_set_source_volume_by_name(
                        context,
                        audioDevice->name,
                        &newVolume,
                        setupSourceCallback,
                        nullptr
                    );
                    if (volumeOperation) {
                        MK::LOG_I(
                            "Set source: {}, volume: {}%",
                            audioDevice->description,
                            audioDevice->volume
                        );
                        pa_operation_unref(volumeOperation);
                    }
                    audioDeviceDestroy(audioDevice);
                },
                audioDevice
            );
            pa_threaded_mainloop_unlock(mainloop);
        },
        audioDevice
    )
        .detach();
}

void
setAudioSinkVolume(const AudioDeviceRef device, const int volume) {
    auto audioDevice = audioDeviceCopy(device);
    audioDevice->volume = volume;
    std::thread(
        [](AudioDeviceRef audioDevice) {
            pa_threaded_mainloop_lock(mainloop);
            pa_threaded_mainloop_once_unlocked(
                mainloop,
                [](pa_threaded_mainloop *mainloop, void *userdata) {
                    auto audioDevice = static_cast<AudioDeviceRef>(userdata);

                    auto percent = audioDevice->volume / 100.0;
                    pa_cvolume newVolume;
                    pa_cvolume_reset(&newVolume, audioDevice->channels);
                    pa_cvolume_set(
                        &newVolume,
                        audioDevice->channels,
                        PA_VOLUME_NORM * percent
                    );

                    auto muteOperation = pa_context_set_sink_mute_by_name(
                        context,
                        audioDevice->name,
                        audioDevice->volume == 0,
                        setupSinkCallback,
                        nullptr
                    );
                    if (muteOperation) pa_operation_unref(muteOperation);

                    auto volumeOperation = pa_context_set_sink_volume_by_name(
                        context,
                        audioDevice->name,
                        &newVolume,
                        setupSinkCallback,
                        nullptr
                    );
                    if (volumeOperation) {
                        MK::LOG_I(
                            "Set sink: {}, volume: {}%",
                            audioDevice->description,
                            audioDevice->volume
                        );
                        pa_operation_unref(volumeOperation);
                    }
                    audioDeviceDestroy(audioDevice);
                },
                audioDevice
            );
            pa_threaded_mainloop_unlock(mainloop);
        },
        audioDevice
    )
        .detach();
}

void
setDefaultAudioSink(const AudioDeviceRef device) {
    auto audioDevice = audioDeviceCopy(device);
    std::thread(
        [](AudioDeviceRef audioDevice) {
            pa_threaded_mainloop_lock(mainloop);
            pa_threaded_mainloop_once_unlocked(
                mainloop,
                [](pa_threaded_mainloop *mainloop, void *userdata) {
                    auto audioDevice = static_cast<AudioDeviceRef>(userdata);

                    auto setDefaultOperation = pa_context_set_default_sink(
                        context,
                        audioDevice->name,
                        setupSinkCallback,
                        nullptr
                    );
                    if (setDefaultOperation) pa_operation_unref(setDefaultOperation);
                    audioDeviceDestroy(audioDevice);
                },
                audioDevice
            );
            pa_threaded_mainloop_unlock(mainloop);
        },
        audioDevice
    )
        .detach();
}

void
setDefaultAudioSource(const AudioDeviceRef device) {
    auto audioDevice = audioDeviceCopy(device);
    std::thread(
        [](AudioDeviceRef audioDevice) {
            pa_threaded_mainloop_lock(mainloop);
            pa_threaded_mainloop_once_unlocked(
                mainloop,
                [](pa_threaded_mainloop *mainloop, void *userdata) {
                    auto audioDevice = static_cast<AudioDeviceRef>(userdata);

                    auto setDefaultOperation = pa_context_set_default_source(
                        context,
                        audioDevice->name,
                        setupSourceCallback,
                        nullptr
                    );
                    if (setDefaultOperation) pa_operation_unref(setDefaultOperation);
                    audioDeviceDestroy(audioDevice);
                },
                audioDevice
            );
            pa_threaded_mainloop_unlock(mainloop);
        },
        audioDevice
    )
        .detach();
}

VideoCaptureDeviceRef
generateVideoDevice(const std::string &path) {
    auto fd = open(path.c_str(), O_RDWR);
    if (fd) {
        v4l2_capability cap;
        ioctl(fd, VIDIOC_QUERYCAP, &cap);
        auto isCapture = V4L2_TYPE_IS_CAPTURE(cap.capabilities);
        if (!isCapture) {
            return nullptr;
        }

        v4l2_fmtdesc fmt;
        fmt.index = 0;
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        if (ioctl(fd, VIDIOC_ENUM_FMT, &fmt)) {
            return nullptr;
        }

        auto index = path.substr(strlen("/dev/video"), path.length());
        auto busInfo = g_ascii_strdown(
            (gchar *)cap.bus_info, strlen((gchar *)cap.bus_info)
        );
        auto device = videoCaptureDeviceCreate(
            atoi(index.c_str()),
            path.c_str(),
            (char *)cap.card,
            strncmp(busInfo, "usb", 3) == 0
        );
        return device;
    }
    return nullptr;
}

void
listCurrentVideoDevices(udev *udev) {
    auto videoCaptureDevices = (std::vector<VideoCaptureDeviceRef> *)vectorCreate();

    auto enumerate = udev_enumerate_new(udev);
    udev_enumerate_add_match_subsystem(enumerate, "video4linux");
    udev_enumerate_scan_devices(enumerate);
    auto devices = udev_enumerate_get_list_entry(enumerate);
    udev_list_entry *entry;
    udev_list_entry_foreach(entry, devices) {
        auto entryPath = udev_list_entry_get_name(entry);
        auto device = udev_device_new_from_syspath(udev, entryPath);
        auto path = udev_device_get_devnode(device);
        auto videoDevice = generateVideoDevice(path);
        if (videoDevice) {
            videoCaptureDevices->push_back(videoDevice);
        }
        udev_device_unref(device);
    }
    udev_enumerate_unref(enumerate);

    MK::Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_videoCapturedDeviceChanged,
            {.as_ptr = videoCaptureDevices},
            0,
            [](void *ptr) {
                auto devices = (std::vector<VideoCaptureDeviceRef> *)ptr;
                auto newDevices = (std::vector<VideoCaptureDeviceRef> *)vectorCreate();
                for (auto device : *devices) {
                    newDevices->push_back(videoCaptureDeviceCopy(device));
                }
                return (void *)newDevices;
            },
            [](void *ptr) {
                vectorDestroy(ptr, (FreeElement)videoCaptureDeviceDestroy);
            }
        )
    );
}

struct udev *udev = nullptr;
std::atomic_bool _isListeningVideoDevices = false;
void
startListenVideoCaptureDevices() {
    if (_isListeningVideoDevices) {
        if (udev) {
            listCurrentVideoDevices(udev);
        }
        return;
    }
    _isListeningVideoDevices = true;
    std::thread([]() {
        udev = udev_new();
        if (!udev) {
            MK::LOG_E("{}", "udev_new failed!");
            return;
        }

        // 枚举现有设备
        listCurrentVideoDevices(udev);

        // 监听插拔事件
        auto monitor = udev_monitor_new_from_netlink(udev, "udev");
        if (!monitor) {
            MK::LOG_E("{}", "udev_monitor_new_from_netlink failed!");
            udev_unref(udev);
            return;
        }
        udev_monitor_filter_add_match_subsystem_devtype(
            monitor, "video4linux", nullptr
        );
        udev_monitor_enable_receiving(monitor);
        auto fd = udev_monitor_get_fd(monitor);

        while (true) {
            fd_set fds;
            FD_ZERO(&fds);
            FD_SET(fd, &fds);
            auto ret = select(fd + 1, &fds, nullptr, nullptr, nullptr);
            if (ret > 0 && FD_ISSET(fd, &fds)) {
                auto device = udev_monitor_receive_device(monitor);
                if (device) {
                    auto action = udev_device_get_action(device);
                    if (action && strcmp(action, "add") == 0) {
                        listCurrentVideoDevices(udev);
                    } else if (action && strcmp(action, "remove") == 0) {
                        listCurrentVideoDevices(udev);
                    }
                    udev_device_unref(device);
                }
            } else {
                MK::LOG_E("{}", "Video device monitoring failure!");
                break;
            }
        }
        udev_monitor_unref(monitor);
        udev_unref(udev);
    }).detach();
}

std::atomic_bool _isOnline = false;
void
setDeviceIsOnline(const bool isOnline) {
    _isOnline = isOnline;
}

bool
getDeviceIsOnline() {
    return _isOnline;
}
