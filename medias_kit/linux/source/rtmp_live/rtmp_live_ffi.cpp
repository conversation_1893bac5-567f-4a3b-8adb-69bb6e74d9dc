#include "rtmp_live/rtmp_live.hpp"

#include "rtmp_live/rtmp_live_ffi.h"

RtmpLiveRef
rtmpLiveCreate(
    const char *const streamType,
    const char *const appId,
    const char *const url,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels,
    const char *const videoHandlerPluginId,
    const bool needVideoHandle
) {
    auto rtmpLive = new MK::RtmpLive(
        std::make_shared<const std::string>(streamType),
        std::make_shared<const std::string>(appId),
        std::make_shared<const std::string>(url),
        videoCaptureDevice,
        std::make_shared<const std::string>(audioCaptureName),
        width,
        height,
        framerate,
        bitrate,
        sampleRate,
        numChannels,
        std::make_shared<const std::string>(videoHandlerPluginId),
        needVideoHandle
    );
    return rtmpLive;
}

void
rtmpLiveDestroy(RtmpLiveRef rtmpLive) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    delete rtmpLivePtr;
}

bool
rtmpLiveInit(RtmpLiveRef rtmpLive) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    return rtmpLivePtr->init();
}

void
rtmpLiveStart(RtmpLiveRef rtmpLive) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    rtmpLivePtr->start();
}

void
rtmpLiveStop(RtmpLiveRef rtmpLive) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    rtmpLivePtr->stop();
}

void
rtmpLiveSpeak(RtmpLiveRef rtmpLive, bool enable) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    rtmpLivePtr->speak(enable);
}

void
rtmpLiveChangeAudioSource(RtmpLiveRef rtmpLive, const char *const audioCaptureName) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    rtmpLivePtr->updateAudioCapture(
        std::make_shared<const std::string>(audioCaptureName)
    );
}

void
rtmpLivePushRtcRemoteAudio(RtmpLiveRef rtmpLive, const char *const frame, const int frameLen) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    return rtmpLivePtr->pushRtcRemoteAudio(frame, frameLen);
}

void
rtmpLivePushCustomRgbaVideo(
    RtmpLiveRef rtmpLive,
    const uint8_t *const frame,
    const int frameLen,
    const int width,
    const int height
) {
    assert(rtmpLive);
    auto *rtmpLivePtr = static_cast<MK::RtmpLive *>(rtmpLive);
    return rtmpLivePtr->pushCustomRgbaVideo(frame, frameLen, width, height);
}
