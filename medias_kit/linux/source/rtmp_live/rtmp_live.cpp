#include "rtmp_live/rtmp_live.hpp"

#include <cassert>
#include <cstring>

#include "NGIAgoraAudioTrack.h"
#include "NGIAgoraMediaNode.h"
#include "NGIAgoraMediaNodeFactory.h"
#include "codec/audio_encoder.hpp"
#include "helper/helper_ffi.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace MK {

RtmpLive::RtmpLive(
    const std::shared_ptr<const std::string> streamType,
    const std::shared_ptr<const std::string> appId,
    const std::shared_ptr<const std::string> url,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const std::shared_ptr<const std::string> audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels,
    const std::shared_ptr<const std::string> videoHandlerPluginId,
    const bool needVideoHandle
)
    : localAudioStream(
          new LocalAudioStream(
              this,
              audioCaptureName,
              sampleRate,
              numChannels
          )
      ),
      localVideoStream(
          new LocalVideoStream(
              this,
              videoCaptureDevice,
              width,
              height,
              framerate,
              bitrate
          )
      ),
      streamType(streamType),
      appId(appId),
      url(url),
      videoHandlerPluginId(videoHandlerPluginId),
      needVideoHandle(needVideoHandle) {}

RtmpLive::~RtmpLive() {
    pause = true;
    localVideoStream.reset();
    localAudioStream.reset();
    if (connection) {
        connection->getRtmpLocalUser()->unregisterRtmpUserObserver(this);
        connection->unregisterObserver(this);
        connection->disconnect();
        factory = nullptr;
        connection = nullptr;
    }
    if (service) {
        service->release();
    }
    LOG_I("{}", "Memory free!");
}

bool
RtmpLive::init() {
    if (isInit) {
        return isInit;
    }
    // 创建服务
    service = createAgoraService();
    if (!service) {
        LOG_E("{}", "createAgoraService failure!");
        return isInit;
    }

    // 服务配置
    agora::base::AgoraServiceConfiguration serviceConfig;
    const char *home = std::getenv("HOME");
    auto baseDir = std::string(home) + "/Documents/v202310/agora.logs/rtmp_" +
                   *streamType + "/";

    serviceConfig.appId = appId->c_str();
    serviceConfig.logConfig.filePath = baseDir.c_str();
    serviceConfig.enableAudioProcessor = true;
    serviceConfig.enableAudioDevice = false;
    serviceConfig.domainLimit = true;

    // 初始化服务
    if (agora::ERR_OK != service->initialize(serviceConfig)) {
        LOG_E("{}", "service initialize failure!");
        return isInit;
    }

    // 连接配置
    agora::rtc::RtmpConnectionConfiguration connectConfig;
    if (localVideoStream->capture) {
        connectConfig.videoConfig.width = localVideoStream->capture->width;
        connectConfig.videoConfig.height = localVideoStream->capture->height;
        connectConfig.videoConfig.framerate = localVideoStream->capture->framerate;
    }
    connectConfig.videoConfig.bitrate = localVideoStream->bitrate;
    connectConfig.videoConfig.encoderHwSwMode = 1;
    connectConfig.videoConfig.encoderBitrateControlMode = 1;

    connectConfig.audioConfig.sampleRateHz =
        localAudioStream->capture->sampleRate;
    connectConfig.audioConfig.numberOfChannels =
        localAudioStream->capture->numChannels;

    // 初始化连接
    connection = service->createRtmpConnection(connectConfig);
    if (!connection) {
        LOG_E("{}", "service createRtmpConnection failure!");
        return isInit;
    }

    connection->getRtmpLocalUser()->setVideoEnabled(true);

    // 注册连接状态观察者
    auto ret = connection->registerObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 注册用户状态观察者
    ret = connection->getRtmpLocalUser()->registerRtmpUserObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerRtmpUserObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 开始连接
    ret = connection->connect(url->c_str());
    if (agora::ERR_OK != ret) {
        LOG_E("connection connect failure! errcode: {}", ret);
        return isInit;
    }

    // 创建媒体节点工厂
    factory = service->createMediaNodeFactory();
    if (!factory) {
        LOG_E("{}", "service createMediaNodeFactory failure!");
        return isInit;
    }

    // 初始化本地主音频流
    if (!localAudioStream->init()) {
        LOG_E("{}", "mainLocalAudioStream init failure!");
        return isInit;
    }

    // 初始化本地主视频流
    if (!localVideoStream->init()) {
        LOG_E("{}", "mainLocalVideoStream init failure!");
        return isInit;
    }

    pause = false;
    return isInit = true;
}

void
RtmpLive::start() {
    pause = false;
    LOG_I("{}", "start live");
}

void
RtmpLive::stop() {
    pause = true;
    LOG_I("{}", "stop live");
}

void
RtmpLive::speak(bool enable) {
    enableSpeak = enable;
    LOG_I("speak enabled {}", enable);
}

bool
RtmpLive::updateAudioCapture(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    if (!localAudioStream->capture->init(audioCaptureName)) {
        LOG_E("{}", "audioCapture init failure!");
        return false;
    }
    return true;
}

#pragma mark-- ILocalVideoStreamController

agora::agora_refptr<agora::rtc::IVideoFrameSender>
RtmpLive::createVideoFrameSender() const {
    return factory->createVideoFrameSender();
}

agora::agora_refptr<agora::rtc::ILocalVideoTrack>
RtmpLive::createCustomVideoTrack(
    agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
) const {
    return service->createCustomVideoTrack(sender);
}

int
RtmpLive::publishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getRtmpLocalUser()->publishVideo(videoTrack);
}

int
RtmpLive::unpublishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getRtmpLocalUser()->unpublishVideo(videoTrack);
}

bool
RtmpLive::canCommitVideo() const {
    return connected && !pause;
}

bool
RtmpLive::videoHandler(
    const PixelFormat pixelFormat,
    const void *frame,
    const size_t frameLen,
    const size_t width,
    const size_t height
) const {
    if (!needVideoHandle) return false;
    auto transferData = transferDataCreate(
        "",
        (const uint8_t *)frame,
        frameLen,
        width,
        height
    );
    auto message = nativeMessageCreate(
        NativeEvent_rtmpLiveVideoHandler,
        {.as_ptr = transferData},
        (int64_t)this,
        (CopyPtrValue)transferDataCopy,
        (FreePtrValue)transferDataDestroy
    );
    MK::Utils::sendNativeMessage(message, videoHandlerPluginId->c_str());
    return true;
}

#pragma mark-- ILocalAudioStreamController

agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
RtmpLive::createAudioPcmDataSender() const {
    return factory->createAudioPcmDataSender();
}

agora::agora_refptr<agora::rtc::ILocalAudioTrack>
RtmpLive::createCustomAudioTrack(
    agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
) const {
    return service->createCustomAudioTrack(sender);
}

int
RtmpLive::publishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getRtmpLocalUser()->publishAudio(audioTrack);
}

int
RtmpLive::unpublishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getRtmpLocalUser()->unpublishAudio(audioTrack);
}

void
RtmpLive::pushRtcRemoteAudio(const char *const frame, const int frameLen) {
    std::lock_guard<std::mutex> lock(remoteMtx);
    if (rtcAudioFrames.size() >= 50) {
        rtcAudioFrames.pop();
    }
    auto sharedVec = std::make_shared<std::vector<char>>(frame, frame + frameLen);
    rtcAudioFrames.push(sharedVec);
    mainCV.notify_one();
}

void
RtmpLive::pushCustomRgbaVideo(
    const uint8_t *const frame,
    const size_t frameLen,
    const size_t width,
    const size_t height
) {
    assert(needVideoHandle);
    localVideoStream->commitVideo(
        PixelFormat_rgba,
        frame,
        frameLen,
        width,
        height
    );
}

AudioCommitType
RtmpLive::canCommitAudio() const {
    if (connected && enableSpeak && !pause && *streamType == "main") {
        return AudioCommitType::mix;
    }
    return AudioCommitType::remote;
}

const std::shared_ptr<const std::vector<char>>
RtmpLive::audioHandler(const char *frame, std::size_t frameLen) {
    auto frames = std::make_shared<std::vector<char>>(frame, frame + frameLen);

    if (!rtcAudioFrames.empty()) {
        std::unique_lock<std::mutex> lock(remoteMtx);
        mainCV.wait(lock, [this]() {
            return !rtcAudioFrames.empty();
        });
        auto remoteFrames = rtcAudioFrames.front();
        rtcAudioFrames.pop();

        if (remoteFrames && !remoteFrames->empty()) {
            if (frameLen <= 0) {
                return remoteFrames;
            } else {
                AudioEncoder::mixAudio(*remoteFrames, *frames);
            }
        }
    }

    return frames;
}

#pragma mark-- IRtmpConnectionObserver

void
RtmpLive::onReconnecting(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLive::onConnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = true;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLive::onReconnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = true;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLive::onDisconnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLive::onConnectionFailure(
    const agora::rtc::RtmpConnectionInfo &connectionInfo,
    agora::rtc::RTMP_CONNECTION_ERROR errCode
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLive::onTransferStatistics(
    uint64_t video_width,
    uint64_t video_height,
    uint64_t video_bitrate,
    uint64_t audio_bitrate,
    uint64_t video_frame_rate,
    uint64_t push_video_frame_cnt,
    uint64_t pop_video_frame_cnt
) {}

#pragma mark-- IRtmpLocalUserObserver

void
RtmpLive::onAudioTrackPublishSuccess(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) {
    LOG_I("{}", "publish audio success!");
}

void
RtmpLive::onAudioTrackPublicationFailure(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack,
    agora::rtc::PublishAudioError error
) {
    LOG_E("{}", "publish audio failure!");
}

void
RtmpLive::onVideoTrackPublishSuccess(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) {
    LOG_I("{}", "publish video success!");
}

void
RtmpLive::onVideoTrackPublicationFailure(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
    agora::rtc::PublishVideoError error
) {
    LOG_E("{}", "publish video failure!");
}

} // namespace MK
