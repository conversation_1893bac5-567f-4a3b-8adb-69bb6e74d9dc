#include "stream&capture/video_capture.hpp"

#include <cstdio>
#include <cstring>
#include <future>
#include <map>
#include <memory>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <string>
#include <thread>
#include <vector>

#include "LibMWCapture/MWCapture.h"
#include "MWFOURCC.h"
#include "device/device_ffi.h"
#include "utils/logger.hpp"

namespace MK {

class InternalCapture {

    std::mutex mutex;

    std::map<const std::string, std::set<VideoCapture *>> observers;

    std::map<const std::string, cv::VideoCapture> captures;

    InternalCapture() = default;

    ~InternalCapture() = default;

  public:
    static InternalCapture &
    share() {
        static InternalCapture capture;
        return capture;
    }

    bool
    addObserver(VideoCapture *observer) {
        assert(observer != nullptr);
        auto devicePath = observer->getVideoCaptureDevice().path;
        std::lock_guard<std::mutex> lock(mutex);
        observers[devicePath].insert(observer);

        auto &capture = captures[devicePath];
        if (!capture.isOpened()) {
            auto isSuccess = capture.open(devicePath, cv::CAP_V4L2);
            if (!isSuccess) {
                LOG_E("Open {} failed!", *devicePath);
                return false;
            }
            capture.set(cv::CAP_PROP_FRAME_WIDTH, observer->width);
            capture.set(cv::CAP_PROP_FRAME_HEIGHT, observer->height);
            capture.set(cv::CAP_PROP_FPS, observer->framerate);
            capture.set(cv::CAP_PROP_FORMAT, CV_8UC4);

            std::thread([this, devicePath]() {
                auto &capture = captures[devicePath];

                cv::Mat originFrame;
                cv::Mat rgbaFrame;
                cv::Mat i420Frame;
                while (true) {
                    try {
                        capture >> originFrame;
                        if (originFrame.empty()) continue;

                        cv::cvtColor(originFrame, rgbaFrame, cv::COLOR_BGRA2RGBA);
                        auto rgbaFrameLen = rgbaFrame.total() * rgbaFrame.elemSize();

                        cv::cvtColor(originFrame, i420Frame, cv::COLOR_BGRA2YUV_I420);
                        auto i420FrameLen = i420Frame.total() * i420Frame.elemSize();

                        std::lock_guard<std::mutex> lock(mutex);
                        auto &observers = this->observers[devicePath];
                        if (observers.empty()) {
                            capture.release();
                            // captures.erase(*devicePath);
                            break;
                        }
                        for (auto observer : observers) {
                            if (observer->pixelFormat == PixelFormat_rgba) {
                                VideoCapture::onCaptureCallback(
                                    rgbaFrame.data,
                                    rgbaFrameLen,
                                    observer
                                );
                            } else {
                                VideoCapture::onCaptureCallback(
                                    i420Frame.data,
                                    i420FrameLen,
                                    observer
                                );
                            }
                        }
                    } catch (cv::Exception error) {
                        LOG_E("{}", error.what());
                        break;
                    }
                }
                LOG_I("{}", "OpenCV capture thread free!");
            }).detach();
        }
        return true;
    }

    void
    removeObserver(VideoCapture *observer) {
        std::lock_guard<std::mutex> lock(mutex);
        observers[observer->videoCaptureDevice->path].erase(observer);
    }
};

void
VideoCapture::onCaptureCallback(
    unsigned char *frame,
    long frameLen,
    void *param
) {
    auto capture = (VideoCapture *)param;
    std::lock_guard<std::mutex> lock(capture->mtx);
    if (capture->channel) {
        MWCAP_VIDEO_SIGNAL_STATUS signalStatus;
        MWGetVideoSignalStatus(capture->channel, &signalStatus);
        capture->_hasSignal = signalStatus.state > MWCAP_VIDEO_SIGNAL_UNSUPPORTED;
    }
    if (capture->frameCallBack) {
        capture->frameCallBack(capture->pixelFormat, frame, frameLen);
    }
}

VideoCapture::VideoCapture(
    const VideoCaptureDeviceRef videoCaptureDevice,
    const PixelFormat pixelFormat,
    const int width,
    const int height,
    const int framerate,
    const FrameCallBack frameCallBack
)
    : videoCaptureDevice(videoCaptureDevice),
      frameCallBack(frameCallBack),
      pixelFormat(pixelFormat),
      width(width),
      height(height),
      framerate(framerate) {
}

VideoCapture::~VideoCapture() {
    isInit = false;
    frameCallBack = nullptr;
    if (videoCaptureDevice->isUsbExtend) {
        InternalCapture::share().removeObserver(this);
    }
    if (handle) {
        MWDestoryVideoCapture(handle);
    }
    if (channel) {
        MWCloseChannel(channel);
    }
    LOG_I("{}", "Memory free!");
}

bool
VideoCapture::init() {
    if (isInit) {
        return isInit;
    }
    if (videoCaptureDevice->isUsbExtend) {
        return isInit = InternalCapture::share().addObserver(this);
    } else {
        if (!MWCaptureInitInstance()) {
            MK::LOG_E("{}", "MWCaptureInitInstance failure!");
            return false;
        }
        if (MW_SUCCEEDED != MWRefreshDevice()) {
            MK::LOG_E("{}", "MWRefreshDevice failure!");
            return false;
        }
        channel = MWOpenChannelByPath(videoCaptureDevice->path);
        if (!channel) {
            LOG_E("{}", "MWOpenChannelByPath failure!");
            return false;
        }
        // 这里使用 async, 是因为美乐威的 MWCreateVideoCapture 函数, 在 F5 调试时会卡死 UI 线程
        auto future = std::async([&]() {
            return MWCreateVideoCapture(
                channel,
                width,
                height,
                pixelFormat == PixelFormat_rgba ? MWFOURCC_RGBA : MWFOURCC_NV12,
                10000000 / framerate,
                onCaptureCallback,
                this
            );
        });
        handle = future.get();
        if (!handle) {
            LOG_E("{}", "MWCreateVideoCapture failure!");
            return false;
        }
    }
    return isInit = true;
}

const VideoCaptureDevice &
VideoCapture::getVideoCaptureDevice() const {
    return *videoCaptureDevice;
}

bool
VideoCapture::hasSignal() const {
    return _hasSignal;
}

} // namespace MK
