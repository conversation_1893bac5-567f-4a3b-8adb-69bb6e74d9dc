#include "stream&capture/local_stream.hpp"

#include <cstring>
#include <iostream>

#include "NGIAgoraAudioTrack.h"
#include "NGIAgoraMediaNode.h"
#include "NGIAgoraVideoTrack.h"

namespace MK {

LocalVideoStream::LocalVideoStream(
    const ILocalVideoStreamController *const controller,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const int width,
    const int height,
    const int framerate,
    const int bitrate
)
    : controller(controller),
      capture(
          videoCaptureDevice ? new VideoCapture(
                                   videoCaptureDevice,
                                   videoCaptureDevice->isUsbExtend ? PixelFormat_i420 : PixelFormat_rgba,
                                   width,
                                   height,
                                   framerate,
                                   [this, width, height](
                                       const PixelFormat pixelFormat,
                                       const uint8_t *const buffer,
                                       const long bufferLen
                                   ) {
                                       if (this->controller->videoHandler(
                                               pixelFormat,
                                               buffer,
                                               bufferLen,
                                               width,
                                               height
                                           )) return;

                                       this->commitVideo(
                                           pixelFormat,
                                           buffer,
                                           bufferLen
                                       );
                                   }
                               )
                             : nullptr
      ),
      bitrate(bitrate) {}

LocalVideoStream::~LocalVideoStream() {
    if (capture) {
        delete capture;
    }
    if (track) {
        controller->unpublishVideo(track);
    }
    LOG_I("{}", "Memory free!");
}

bool
LocalVideoStream::init() {
    sender = controller->createVideoFrameSender();

    track = controller->createCustomVideoTrack(sender);

    agora::rtc::VideoEncoderConfiguration codecConfig;
    codecConfig.codecType = agora::rtc::VIDEO_CODEC_H264;
    codecConfig.advanceOptions.encodingPreference = agora::rtc::PREFER_HARDWARE;
    if (capture) {
        codecConfig.dimensions = {capture->width, capture->height};
        codecConfig.frameRate = capture->framerate;
    }
    codecConfig.bitrate = bitrate * 1000;
    codecConfig.degradationPreference = agora::rtc::MAINTAIN_QUALITY;
    track->setVideoEncoderConfiguration(codecConfig);

    track->setEnabled(true);
    auto ret = controller->publishVideo(track);
    if (ret != agora::ERR_OK) {
        LOG_E("Publish video failed! error code: {}", ret);
        return false;
    }

    if (capture && !capture->init()) {
        LOG_E("{}", "VideoCapture init failure!");
        return false;
    }

    return true;
}

bool
LocalVideoStream::commitVideo(
    const PixelFormat pixelFormat,
    const void *frame,
    const size_t frameLen,
    const size_t width,
    const size_t height
) {
    if (!controller->canCommitVideo()) {
        return false;
    }
    agora::media::base::ExternalVideoFrame videoFrame;
    videoFrame.type = agora::media::base::ExternalVideoFrame::VIDEO_BUFFER_RAW_DATA;
    if (capture) {
        videoFrame.stride = capture->width;
        videoFrame.height = capture->height;
    } else {
        videoFrame.stride = width;
        videoFrame.height = height;
    }
    agora::media::base::VIDEO_PIXEL_FORMAT agoraPixelFormat[3] = {
        agora::media::base::VIDEO_PIXEL_NV12,
        agora::media::base::VIDEO_PIXEL_I420,
        agora::media::base::VIDEO_PIXEL_RGBA,
    };
    videoFrame.format = agoraPixelFormat[pixelFormat];
    videoFrame.buffer = const_cast<void *>(frame);
    auto ret = sender->sendVideoFrame(videoFrame);
    if (agora::ERR_OK != ret) {
        LOG_E("sendVideoFrame failure! errcode: {}", ret);
        return false;
    }
    return true;
}

LocalAudioStream::LocalAudioStream(
    ILocalAudioStreamController *controller,
    const std::shared_ptr<const std::string> audioCaptureName,
    const int sampleRate,
    const int numChannels
)
    : controller(controller),
      capture(
          new AudioCapture(
              audioCaptureName,
              sampleRate,
              numChannels,
              [this](char *buffer, long bufferLen) {
                  this->commitAudio(buffer, bufferLen);
              }
          )
      ) {}

LocalAudioStream::~LocalAudioStream() {
    delete capture;
    if (track) {
        controller->unpublishAudio(track);
    }
    LOG_I("{}", "Memory free!");
}

bool
LocalAudioStream::init() {
    sender = controller->createAudioPcmDataSender();

    track = controller->createCustomAudioTrack(sender);

    track->setEnabled(true);
    auto ret = controller->publishAudio(track);
    if (ret) {
        LOG_E("{}", "Publish audio failed!");
        return false;
    }

    if (!capture->init()) {
        LOG_E("{}", "AudioCapture init failure!");
        return false;
    }
    return true;
}

bool
LocalAudioStream::commitAudio(const void *frame, size_t frameLen) {
    auto commitType = controller->canCommitAudio();
    if (commitType == AudioCommitType::disable) {
        return false;
    } else if (commitType == AudioCommitType::remote) {
        auto remoteAudio = controller->audioHandler((const char *)frame, 0);
        memcpy(const_cast<void *>(frame), remoteAudio->data(), remoteAudio->size());
        frameLen = remoteAudio->size();
    } else if (commitType == AudioCommitType::mix) {
        auto mixAudio = controller->audioHandler((const char *)frame, frameLen);
        if (!mixAudio->empty()) {
            memcpy(const_cast<void *>(frame), mixAudio->data(), mixAudio->size());
            frameLen = mixAudio->size();
        }
    }

    if (frameLen <= 0) {
        return false;
    }

    int sampleSize = sizeof(int16_t) * capture->numChannels;
    int samplesPer10ms = capture->sampleRate / 100;
    int sendBytes = sampleSize * samplesPer10ms;

    // warning: 注意采样大小是否是 10ms 数据的倍数
    auto count = frameLen / sendBytes;

    for (int i = 0; i < count; i++) {
        auto ptr = (uint8_t *)frame + (i * sendBytes);

        auto ret = sender->sendAudioPcmData(
            ptr,
            0,
            0,
            samplesPer10ms,
            agora::rtc::TWO_BYTES_PER_SAMPLE,
            capture->numChannels,
            capture->sampleRate
        );
        if (agora::ERR_OK != ret) {
            LOG_E("{}", "sendAudioPcmData failure! errcode: {}", ret);
            return false;
        }
    }
    return true;
}

} // namespace MK