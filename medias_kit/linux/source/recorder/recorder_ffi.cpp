#include "recorder/recorder_ffi.h"

#include "recorder/recorder.hpp"
#include "utils/logger.hpp"

VideoRecordInfoRef
videoRecordInfoCreate(
    const char *const name,
    const char *const path,
    const int64_t pointOffset,
    const int64_t frameCount
) {
    auto info = new VideoRecordInfo();
    if (name) info->name = strdup(name);
    if (path) info->path = strdup(path);
    info->pointOffset = pointOffset;
    info->frameCount = frameCount;
    return info;
}

VideoRecordInfoRef
videoRecordInfoCopy(const VideoRecordInfoRef info) {
    return videoRecordInfoCreate(
        info->name,
        info->path,
        info->pointOffset,
        info->frameCount
    );
}

void
videoRecordInfoDestroy(const VideoRecordInfoRef info) {
    if (info->name) free(info->name);
    if (info->path) free(info->path);
    delete info;
}

RecorderRef
recorderCreate(
    const char *const spaceName,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int videoBitrate,
    const int sampleRate,
    const int audioBitrate,
    const int numChannels
) {
    auto *recorder = new MK::Recorder(
        std::make_shared<const std::string>(spaceName),
        videoCaptureDevice,
        std::make_shared<const std::string>(audioCaptureName),
        width,
        height,
        framerate,
        videoBitrate,
        sampleRate,
        audioBitrate,
        numChannels
    );
    return recorder;
}

void
recorderDestroy(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    delete recorderPtr;
}

bool
recorderInit(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    return recorderPtr->init();
}

void
recorderStart(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    recorderPtr->start();
}

void
recorderStop(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    recorderPtr->stop();
}

void
recorderRecordAudio(RecorderRef recorder, bool enable) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    recorderPtr->recordAudio(enable);
}

bool
recorderUpdateAudioCapture(
    RecorderRef recorder,
    const char *const audioCaptureName
) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    return recorderPtr->updateAudioCapture(
        std::make_shared<const std::string>(audioCaptureName)
    );
}

void
recorderRecordRemoteAudio(RecorderRef recorder, char *frame, int frameLen) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    recorderPtr->recordRemoteAudio(frame, frameLen);
}

void
recorderMerge(
    RecorderRef recorder,
    const char *const name,
    const long beginTime,
    const long endTime,
    const bool needEndPrecision
) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    recorderPtr->merge(
        std::make_shared<const std::string>(name),
        beginTime,
        endTime,
        needEndPrecision
    );
}

int64_t
recorderFrameCount(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    return recorderPtr->frameCount();
}

int64_t
recorderFirstFrameTimestampWithOffset(RecorderRef recorder) {
    assert(recorder);
    auto *recorderPtr = static_cast<MK::Recorder *>(recorder);
    return recorderPtr->firstFrameTimestampWithOffset();
}
