#include "recorder/recorder.hpp"

#include <cassert>
#include <cstdint>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <fstream>
#include <future>
#include <iostream>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <string>
#include <system_error>
#include <thread>
#include <vector>

#include "types/types_ffi.h"
#include "types/video_stream.hpp"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace MK {

namespace fs = std::filesystem;

int
extractNum(const std::string &str, char delimiter) {
    size_t pos = str.find(delimiter);
    if (pos != std::string::npos) {
        return std::stoi(str.substr(pos + 1));
    }
    return 0;
}

void
fixRealTimeAndIndex(
    long &inTime,
    long &index,
    const std::vector<VideoStream> &streams,
    const bool isRoundUp
) {
    auto &videoInfo = streams[index];
    long nextIndex = index;
    if (inTime > videoInfo.timestamp) {
        while (nextIndex < streams.size()) {
            nextIndex = index + 1;
            auto &nextVideoInfo = streams[nextIndex];
            if (inTime < nextVideoInfo.timestamp) {
                if (isRoundUp) {
                    inTime = nextVideoInfo.timestamp;
                    index = nextIndex;
                }
                break;
            } else if (inTime == nextVideoInfo.timestamp) {
                inTime = nextVideoInfo.timestamp;
                index = nextIndex;
                break;
            } else {
                index = nextIndex;
            }
        }
    } else if (inTime < videoInfo.timestamp) {
        while (index > 0) {
            nextIndex = index - 1;
            auto &nextVideoInfo = streams[nextIndex];
            if (inTime > nextVideoInfo.timestamp) {
                if (isRoundUp) {
                    inTime = streams[index].timestamp;
                } else {
                    inTime = nextVideoInfo.timestamp;
                    index = nextIndex;
                }
                break;
            } else if (inTime == nextVideoInfo.timestamp) {
                inTime = nextVideoInfo.timestamp;
                index = nextIndex;
                break;
            } else {
                index = nextIndex;
            }
        }
    }
    inTime = streams[index].timestamp;
}

Recorder::Recorder(
    const std::shared_ptr<const std::string> spaceName,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const std::shared_ptr<const std::string> audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int videoBitrate,
    const int sampleRate,
    const int audioBitrate,
    const int numChannels
)
    : videoCapture(
          new VideoCapture(
              videoCaptureDevice,
              videoCaptureDevice->isUsbExtend ? PixelFormat_i420 : PixelFormat_nv12,
              width,
              height,
              framerate,
              [this](
                  const PixelFormat pixelFormat,
                  const uint8_t *const buffer,
                  const long bufferLen
              ) {
                  this->encodeVideoFrame(buffer, bufferLen);
              }
          )
      ),
      audioCapture(
          new AudioCapture(
              audioCaptureName,
              sampleRate,
              numChannels,
              [this](char *buffer, long bufferLen) {
                  this->encodeAudioFrame(buffer, bufferLen);
              }
          )
      ),
      videoEncoder(
          new VideoEncoder(
              videoCapture->pixelFormat,
              width,
              height,
              framerate,
              videoBitrate,
              [this](
                  const uint8_t *package,
                  uint32_t packageLen,
                  mw_venc_frame_info_t *packageInfo
              ) {
                  this->writeVideoPackage(package, packageLen, packageInfo);
              }
          )
      ),
      audioEncoder(
          new AudioEncoder(
              sampleRate,
              audioBitrate,
              numChannels,
              [this](
                  const uint8_t *package,
                  uint32_t packageLen,
                  uint64_t pts
              ) {
                  this->writeAudioPackage(package, packageLen, pts);
              }
          )
      ),
      spaceName(spaceName) {}

Recorder::~Recorder() {
    pause = true;
    isInit = false;
    delete audioCapture;
    delete audioEncoder;
    delete videoCapture;
    delete videoEncoder;
    if (handle) {
        closeMp4(true);
    }
    LOG_I("{}", "Memory free!");
}

bool
Recorder::init() {
    if (isInit) {
        return isInit;
    }
    const char *home = std::getenv("HOME");
    if (!home) {
        LOG_E("{}", "getenv failure!");
        return isInit;
    }
    baseDir = std::string(home) + "/Documents/v202310/";
    if (!fs::exists(baseDir)) {
        std::error_code errCode;
        if (!fs::create_directories(baseDir, errCode)) {
            LOG_E("{}", errCode.message());
            return isInit;
        }
    }
    spaceDir = baseDir + *spaceName + "/";
    recordDir = spaceDir + "recorded/";
    streamDir = spaceDir + "streams/";
    playbackDir = spaceDir + "playback/";
    indexPath = spaceDir + "index";
    breakOffsetPath = spaceDir + "break_offset";

    if (!fs::exists(spaceDir)) {
        std::error_code errCode;
        if (!fs::create_directories(spaceDir, errCode)) {
            LOG_E("{}", errCode.message());
            return isInit;
        }
    }
    std::ofstream videoInfo(spaceDir + "video_info.txt");
    videoInfo << "{"
                 "\"framerate\":"
              << videoCapture->framerate
              << ","
                 "\"width\":"
              << videoCapture->width
              << ","
                 "\"height\":"
              << videoCapture->height
              << ","
                 "\"bitrate\":"
              << videoEncoder->bitrate << ","
              << "\"streams\":"
              << "[\"video\",\"audio\"]"
              << "}" << std::endl;
    videoInfo.flush();
    videoInfo.close();
    if (!audioEncoder->init()) {
        LOG_E("{}", "audioEncoder init failure!");
        return isInit;
    }
    auto future = std::async([this]() {
        // 异步初始化, 防止阻塞(美乐威SDK中的编码器, 在UI线程上初始化会高概率卡死线程)
        return videoEncoder->init();
    });
    if (!future.get()) {
        LOG_E("{}", "videoEncoder init failure!");
        return isInit;
    }
    if (!audioCapture->init()) {
        LOG_E("{}", "audioCapture init failure!");
        return isInit;
    }
    if (!videoCapture->init()) {
        LOG_E("{}", "videoCapture init failure!");
        return isInit;
    }
    pause = false;

    fs::path path(spaceDir + "stream_list");
    if (fs::exists(path)) {
        // 获取最后一次写入 UTC 时间戳
        auto last_time = std::filesystem::last_write_time(path);
        // 转换为系统时间点
        auto last_epoch_ts = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            last_time - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now()
        );
        // 转换为毫秒时间戳
        auto last_timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(last_epoch_ts.time_since_epoch()).count();
        // 获取当前 UTC 时间戳
        const auto now = std::chrono::system_clock::now();
        const auto now_ms = std::chrono::time_point_cast<std::chrono::milliseconds>(now);
        const auto epoch_ms = now_ms.time_since_epoch();
        const auto timestamp_ms = static_cast<uint64_t>(epoch_ms.count());
        auto offset = timestamp_ms - last_timestamp_ms + 1000 / videoCapture->framerate;

        if (fs::exists(breakOffsetPath)) {
            std::ifstream breakOffsetStream(breakOffsetPath);
            std::string breakOffsetText;
            std::getline(breakOffsetStream, breakOffsetText);
            breakOffsetStream.close();
            try {
                breakOffset = std::stoull(breakOffsetText);
            } catch (const std::exception &error) {
                LOG_E("break offset file read failure: {}", error.what());
            }
        }
        breakOffset += offset;
        std::ofstream outBreakOffsetStream(breakOffsetPath);
        outBreakOffsetStream << breakOffset;
        outBreakOffsetStream.flush();
        outBreakOffsetStream.close();
    }
    return isInit = true;
}

void
Recorder::start() {
    pause = false;
    LOG_I("{}", "start record!");
}

void
Recorder::stop() {
    pause = true;
    LOG_I("{}", "stop record!");
}

void
Recorder::recordAudio(bool enable) {
    enableRecordAudio = enable;
    LOG_I("record audio enabled {}", enable);
}

bool
Recorder::updateAudioCapture(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    if (!audioCapture->init(audioCaptureName)) {
        LOG_E("{}", "audioCapture init failure!");
        return false;
    }
    return true;
}

void
Recorder::recordRemoteAudio(char *frame, int frameLen) {
    static char *silenceFrame = nullptr;
    if (!silenceFrame) {
        silenceFrame = new char[frameLen];
        memset(silenceFrame, 0, frameLen);
    }

    audioEncoder->encode(
        enableRecordAudio ? frame : silenceFrame,
        frameLen,
        AudioEncoder::AudioType::remote
    );
}

void
Recorder::merge(
    const std::shared_ptr<const std::string> name,
    const long beginTime,
    const long endTime,
    const bool needEndPrecision
) const {
    auto recorderAddress = (int64_t)this;
    auto width = videoCapture->width;
    auto height = videoCapture->height;
    auto framerate = videoCapture->framerate;
    auto spaceDir = this->spaceDir;
    auto streamDir = this->streamDir;
    auto playbackDir = this->playbackDir;

    std::thread mergeTask([recorderAddress,
                           width,
                           height,
                           framerate,
                           spaceDir,
                           streamDir,
                           playbackDir,
                           name,
                           beginTime,
                           endTime,
                           needEndPrecision]() {
        std::ifstream streamList(spaceDir + "stream_list", std::ios::in);

        // 解析 stream_list
        std::vector<VideoStream> streams;
        std::string line;
        while (std::getline(streamList, line)) {
            try {
                auto tsEnd = line.find("_", 0);
                auto ts = line.substr(0, tsEnd++);

                auto keyTypeEnd = line.find("_", tsEnd);
                auto keyType = line.substr(tsEnd, keyTypeEnd++ - tsEnd);

                auto delayEnd = line.find("_", keyTypeEnd);
                auto delay = line.substr(keyTypeEnd, delayEnd++ - keyTypeEnd);

                auto size = line.substr(delayEnd);

                streams.push_back(
                    VideoStream(
                        std::stol(ts),
                        keyType == "i",
                        std::stoi(delay),
                        std::stoi(size)
                    )
                );
            } catch (const std::exception &error) {
                LOG_E("stream_list parse line error: {}", error.what());
            }
        }

        streamList.close();
        long pointOffset = 0;
        long frameCount = 0;
        std::string mergeFilePath;
        do {
            mergeFilePath.clear();
            if (streams.empty()) {
                LOG_E("{}", "stream_list is empty!");
                break;
            }

            auto realBeginTime = std::max(beginTime, streams.begin()->timestamp);
            auto realEndTime = std::min(endTime, (streams.end() - 1)->timestamp);

            auto deltaTime = realBeginTime - streams.begin()->timestamp;
            auto startIndex = std::min(deltaTime / 1000 / framerate, long(streams.size() - 1));
            fixRealTimeAndIndex(realBeginTime, startIndex, streams, false);

            deltaTime = realEndTime - streams.begin()->timestamp;
            auto endIndex = std::min(deltaTime / 1000 / framerate, long(streams.size() - 1));
            fixRealTimeAndIndex(realEndTime, endIndex, streams, false);

            std::error_code errorCode;
            if (!fs::exists(playbackDir, errorCode)) {
                std::filesystem::create_directory(playbackDir, errorCode);
            }

            // 开始帧 向前查找第一个 I 帧
            auto keyIndex = startIndex;
            while (keyIndex >= 0) {
                auto videoInfo = streams[keyIndex];
                if (videoInfo.isKeyFrame) {
                    break;
                }
                keyIndex -= 1;
            }

            pointOffset = startIndex - keyIndex;
            startIndex = keyIndex;
            frameCount = endIndex - startIndex + 1;

            if (!needEndPrecision) {
                // 结束帧 向前查找第一个 I 帧
                keyIndex = endIndex;
                while (keyIndex > 0) {
                    auto videoInfo = streams[keyIndex];
                    if (videoInfo.isKeyFrame) {
                        break;
                    }
                    keyIndex -= 1;
                }
                endIndex = keyIndex;
                frameCount = endIndex - startIndex;
            }

            if (frameCount == 0) {
                LOG_E("frameCount: {}", frameCount);
                break;
            }

            // 初始化 mp4 文件
            mergeFilePath = playbackDir + name->c_str() + ".mp4";
            auto handle = mw_mp4_open(mergeFilePath.c_str());
            if (!handle) {
                LOG_E("mw_mp4_open {} failure!", mergeFilePath);
                mergeFilePath.clear();
                break;
            }
            mw_mp4_video_info_t info;
            info.codec_type = MW_MP4_VIDEO_TYPE_H264;
            info.width = width;
            info.height = height;
            info.timescale = 1000;
            info.h264 = {0};

            if (MW_MP4_STATUS_SUCCESS != mw_mp4_set_video(handle, &info)) {
                LOG_E("{}", "mw_mp4_set_video failure!");
                mergeFilePath.clear();
                break;
            }

            auto pts = 0;

            // mp4 写流
            for (int i = 0; i < frameCount; i++) {
                if (mergeFilePath.empty()) break;

                auto videoInfo = streams[startIndex + i];

                auto streamPath = streamDir + std::to_string(videoInfo.timestamp);
                std::ifstream streamFile(streamPath, std::ios::binary);
                if (!streamFile.is_open()) {
                    LOG_E("StreamFile open failure! path: {}", streamPath);
                    continue;
                }
                streamFile.seekg(0, std::ios::end);
                std::streamsize fileSize = streamFile.tellg();
                streamFile.seekg(0, std::ios::beg);
                std::vector<char> buffer(fileSize);
                if (streamFile.read(buffer.data(), fileSize)) {
                    if (MW_MP4_STATUS_SUCCESS !=
                        mw_mp4_write_video(
                            handle,
                            (uint8_t *)buffer.data(),
                            buffer.size(),
                            pts
                        )) {
                        LOG_E("{}", "mw_mp4_write_video failure!");
                        mergeFilePath.clear();
                        break;
                    }
                } else {
                    LOG_E("{}", "streamFile read failure!");
                }
                streamFile.close();

                pts += videoInfo.delay;
            }

            // 关闭mp4
            if (MW_MP4_STATUS_SUCCESS != mw_mp4_close(handle)) {
                LOG_E("{}", "mw_mp4_close failure!");
                mergeFilePath.clear();
                break;
            }
        } while (false);

        auto videoRecordInfo = videoRecordInfoCreate(
            name->c_str(),
            mergeFilePath.c_str(),
            pointOffset,
            frameCount
        );

        Utils::sendNativeMessage(
            nativeMessageCreate(
                NativeEvent_videoMerged,
                {.as_ptr = videoRecordInfo},
                recorderAddress,
                (CopyPtrValue)videoRecordInfoCopy,
                (FreePtrValue)videoRecordInfoDestroy
            )
        );
    });
    mergeTask.detach();
}

int64_t
Recorder::frameCount() const {
    std::error_code errorCode;
    if (fs::is_directory(streamDir, errorCode)) {
        auto begin = fs::directory_iterator(streamDir);
        auto end = fs::directory_iterator();
        return std::distance(begin, end);
    }
    return 0;
}

int64_t
Recorder::firstFrameTimestampWithOffset() {
    if (firstFrameTimestamp) {
        return firstFrameTimestamp + breakOffset;
    }
    fs::path path(spaceDir + "stream_list");
    if (!fs::exists(path)) {
        return 0;
    }
    std::ifstream streamList(path);
    std::string line;
    std::getline(streamList, line);
    streamList.close();
    try {
        auto tsEnd = line.find("_", 0);
        auto ts = line.substr(0, tsEnd++);
        firstFrameTimestamp = std::stoull(ts);
    } catch (const std::exception &error) {
        LOG_E("stream_list parse line error: {}", error.what());
    }
    return firstFrameTimestamp ? firstFrameTimestamp + breakOffset : 0;
}

void
Recorder::encodeVideoFrame(const uint8_t *const frame, long frameLen) {
    if (pause) {
        return;
    }
    videoEncoder->encode(frame, frameLen);
}

void
Recorder::encodeAudioFrame(char *frame, long frameLen) {
    if (pause) {
        return;
    }
    if (!enableRecordAudio) {
        memset(frame, 0, frameLen);
    }
    audioEncoder->encode(frame, frameLen);
}

void
Recorder::writeVideoPackage(
    const uint8_t *package,
    uint32_t packageLen,
    mw_venc_frame_info_t *packageInfo
) {
    if (handle) {
        if (packageCount % 60 == 0) {
            Utils::sendNativeMessage(
                nativeMessageCreate(
                    NativeEvent_videoSignalStatusChanged,
                    {.as_bool = videoCapture->hasSignal()},
                    (int64_t)this,
                    nullptr,
                    nullptr
                )
            );
        }

        // 录制约 10 分钟一个片段
        if (packageCount >= 10 * 60 * videoEncoder->framerate &&
            packageInfo->frame_type == MW_VENC_FRAME_TYPE_IDR) {
            closeMp4(false);
            writeVideoPackage(package, packageLen, packageInfo);
            return;
        }

        if (!fs::exists(streamDir)) {
            // 第一次开启手术录制, 创建路径
            std::error_code errCode;
            if (!fs::create_directories(streamDir, errCode)) {
                LOG_E("{}", errCode.message());
                return;
            }
        }

        // 获取UTC时间戳
        const auto now = std::chrono::system_clock::now();
        const auto now_ms =
            std::chrono::time_point_cast<std::chrono::milliseconds>(now);
        const auto epoch_ms = now_ms.time_since_epoch();
        const auto timestamp_ms = static_cast<uint64_t>(epoch_ms.count());

        // 写二进制文件流
        auto fName = std::to_string(timestamp_ms);
        const auto fPath = streamDir + fName;
        std::ofstream stream(fPath, std::ios::binary);
        stream.write((char *)package, packageLen);
        stream.flush();
        stream.close();

        // 追加文件列表记录
        const auto rPath = spaceDir + "stream_list";
        std::ofstream streamList(rPath, std::ios::app);
        streamList << fName
                   << (packageInfo->frame_type == MW_VENC_FRAME_TYPE_IDR ? "_i"
                                                                         : "_p")
                   << "_" << packageInfo->delay << "_" << packageLen
                   << std::endl;
        streamList.flush();
        streamList.close();

        std::lock_guard<std::mutex> lock(mtx);
        if (MW_MP4_STATUS_SUCCESS != mw_mp4_write_video(
                                         handle,
                                         package,
                                         packageLen,
                                         packageInfo->pts
                                     )) {
            LOG_E("{}", "mw_mp4_write_video failure!");
            return;
        }
        packageCount += 1;
    } else {
        if (newMp4()) {
            writeVideoPackage(package, packageLen, packageInfo);
        }
    }
}

void
Recorder::writeAudioPackage(
    const uint8_t *package,
    uint32_t packageLen,
    uint64_t pts
) {
    std::lock_guard<std::mutex> lock(mtx);
    if (handle) {
        if (MW_MP4_STATUS_SUCCESS != mw_mp4_write_audio(
                                         handle,
                                         package,
                                         packageLen,
                                         pts
                                     )) {
            LOG_E("{}", "mw_mp4_write_audio failure!");
        }
    }
}

bool
Recorder::freeDiskSpace() {
    auto path = fs::path(baseDir + "uploaded/");
    if (!fs::exists(path)) {
        LOG_E("{} not exists", path.string());
        return false;
    }
    // 标准码率下 24h 持续录制需要的空间 + 5G
    auto iter = fs::directory_iterator(path);
    while (fs::space(baseDir).available <
           (videoEncoder->bitrate * 1000 / 8 * 60 * 60 * 24) +
               (5ull * 1024 * 1024 * 1024)) {
        if (iter != fs::directory_iterator()) {
            std::error_code errCode;
            fs::remove_all(iter->path(), errCode);
            if (errCode) {
                LOG_E(
                    "remove_all {} {}", iter->path().string(), errCode.message()
                );
            }
            LOG_I(
                "space available: {}",
                fs::space(baseDir).available / 1024 / 1024 / 1024
            );
            iter++;
        } else {
            break;
        }
    }
    return true;
}

bool
Recorder::newMp4() {
    packageCount = 0;
    // 释放磁盘空间, 暂不需要, 在业务端由用户手动删除
    // if (!freeDiskSpace()) {
    //     LOG_E("{}", "freeDiskSpace failure!");
    //     return;
    // }

    if (!fs::exists(recordDir)) {
        // 第一次开启手术录制, 创建路径
        if (!videoCapture->hasSignal()) {
            return false;
        }
        std::error_code errCode;
        if (!fs::create_directories(recordDir, errCode)) {
            LOG_E("{}", errCode.message());
            return false;
        }
    } else {
        // 继续已开启手术录制
        if (index == 1) {
            // 意外中断导致的恢复录制
            if (fs::exists(indexPath)) {
                // 存在索引记录
                std::ifstream indexStream(indexPath);
                std::string indexText;
                std::getline(indexStream, indexText);
                indexStream.close();
                int num = 0;
                try {
                    num = std::stoi(indexText);
                } catch (const std::exception &error) {
                    LOG_E("index file read failure: {}", error.what());
                }
                if (num > 0) {
                    // 读取正常
                    index = num + 1;
                } else {
                    // 读取异常, 扫描路径全文件
                    if (!fs::is_empty(recordDir)) {
                        // 文件夹不为空, 遍历得出索引
                        auto iterator = fs::directory_iterator(recordDir.c_str());
                        std::vector<int> fileIndexes;
                        for (auto &item : iterator) {
                            auto path = item.path();
                            if (path.extension() == ".mp4") {
                                auto fileName = item.path().stem();
                                fileIndexes.push_back(extractNum(fileName, '_')
                                );
                            }
                        }
                        std::sort(fileIndexes.begin(), fileIndexes.end());
                        if (!fileIndexes.empty()) {
                            index = fileIndexes.back() + 1;
                        }
                    }
                }
            }
        } else {
            // 正常录制
            index += 1;
        }
    }

    std::ostringstream nameStream;
    nameStream << *spaceName << "_" << std::setfill('0') << std::setw(4)
               << index;
    filePath = recordDir + nameStream.str();

    // 检测是存在需要修复的 mp4 文件
    if (fs::exists(filePath)) {
        // 修复文件
        if (MW_MP4_STATUS_SUCCESS == mw_mp4_repair(filePath.c_str(), true)) {
            LOG_I("Repair {}.mp4 success!", nameStream.str());
            // 重命名
            fs::rename(filePath, filePath + ".bad.mp4");

            // 视频索引记录
            std::ofstream indexFile(indexPath);
            indexFile << index;
            indexFile.flush();
            indexFile.close();

            // 增加索引
            index += 1;
            std::ostringstream nameStream;
            nameStream << "_" << std::setfill('0') << std::setw(4) << index;
            filePath = recordDir + *spaceName + nameStream.str();
        } else {
            // 修复失败, 覆盖损坏文件
            LOG_E("Repair {}.mp4 failure!", nameStream.str());
        }
    }

    // 初始化 mp4 文件
    std::lock_guard<std::mutex> lock(mtx);
    handle = mw_mp4_open(filePath.c_str());
    if (!handle) {
        LOG_E("mw_mp4_open {} failure!", filePath);
        return false;
    }
    mw_mp4_video_info_t videoInfo;
    videoInfo.codec_type = MW_MP4_VIDEO_TYPE_H264;
    videoInfo.width = videoEncoder->width;
    videoInfo.height = videoEncoder->height;
    videoInfo.timescale = 1000;
    videoInfo.h264 = {0};
    if (MW_MP4_STATUS_SUCCESS != mw_mp4_set_video(handle, &videoInfo)) {
        LOG_E("{}", "mw_mp4_set_video failure!");
        return false;
    }

    mw_mp4_audio_info_t audioInfo;
    audioInfo.codec_type = MW_MP4_AUDIO_TYPE_ADTS_AAC;
    audioInfo.timescale = 1000;
    // audioInfo.channels = audioCapture->numChannels;
    // audioInfo.sample_rate = audioCapture->sampleRate;
    // audioInfo.profile = 23;
    if (MW_MP4_STATUS_SUCCESS != mw_mp4_set_audio(handle, &audioInfo)) {
        LOG_E("{}", "mw_mp4_set_audio failure!");
        return false;
    }
    return true;
}

void
Recorder::closeMp4(const bool finishRecord) {
    {
        std::lock_guard<std::mutex> lock(mtx);
        if (MW_MP4_STATUS_SUCCESS != mw_mp4_close(handle)) {
            LOG_E("{}", "mw_mp4_close failure!");
            return;
        }
        handle = nullptr;
    }

    // 重命名
    std::error_code errCode;
    fs::rename(filePath, filePath + ".mp4", errCode);
    if (errCode) {
        LOG_E("rename {} {}", filePath, errCode.message());
    }

    // 视频索引记录
    std::ofstream indexFile(indexPath);
    indexFile << index;
    indexFile.flush();
    indexFile.close();

    if (finishRecord) {
        // 录制结束标记文件
        std::ofstream stopFile(spaceDir + "stopped.txt");
        stopFile << index;
        stopFile.flush();
        stopFile.close();
    }
}

} // namespace MK
