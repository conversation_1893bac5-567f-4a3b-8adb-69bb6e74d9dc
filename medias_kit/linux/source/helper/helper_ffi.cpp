#include "helper/helper_ffi.h"

#include <chrono>
#include <cstring>
#include <exception>
#include <filesystem>
#include <fstream>
#include <map>
#include <string>
#include <system_error>
#include <thread>
#include <unistd.h>
#include <vector>

#include "utils/logger.hpp"
#include "utils/utils.hpp"

/// 任务是否可取消
std::map<std::string, bool> taskCancelAbles;

void
copyFileImpl(
    const char *const src,
    const char *const dst,
    const char *const callbackPluginId
) {
    std::ifstream srcFile(src, std::ios::binary);
    if (!srcFile.is_open()) {
        auto info = copyFileInfoCreate(
            dst,
            CopyFileState_failed,
            0,
            0,
            0,
            "Open source file failure!"
        );
        auto message = nativeMessageCreate(
            NativeEvent_fileCopy,
            {.as_ptr = info},
            0,
            (CopyPtrValue)copyFileInfoCopy,
            (FreePtrValue)copyFileInfoDestroy
        );
        MK::Utils::sendNativeMessage(message, callbackPluginId);
        return;
    }
    std::ofstream dstFile(dst, std::ios::binary);
    if (!dstFile.is_open()) {
        auto info = copyFileInfoCreate(
            dst,
            CopyFileState_failed,
            0,
            0,
            0,
            "Open destination file failure!"
        );
        auto message = nativeMessageCreate(
            NativeEvent_fileCopy,
            {.as_ptr = info},
            0,
            (CopyPtrValue)copyFileInfoCopy,
            (FreePtrValue)copyFileInfoDestroy
        );
        MK::Utils::sendNativeMessage(message, callbackPluginId);
        return;
    }

    srcFile.seekg(0, std::ios::end);
    std::streamsize fileSize = srcFile.tellg();
    srcFile.seekg(0, std::ios::beg);
    dstFile.seekp(0, std::ios::beg);

    if (fileSize <= 0) {
        auto info = copyFileInfoCreate(
            dst,
            CopyFileState_failed,
            0,
            0,
            0,
            "Source file size invalid or zero!"
        );
        auto message = nativeMessageCreate(
            NativeEvent_fileCopy,
            {.as_ptr = info},
            0,
            (CopyPtrValue)copyFileInfoCopy,
            (FreePtrValue)copyFileInfoDestroy
        );
        MK::Utils::sendNativeMessage(message, callbackPluginId);
        return;
    }

    std::vector<char> buffer(1024 * 1024);
    int64_t copiedSize = 0;
    auto startTime = std::chrono::steady_clock::now();
    auto lastCallbackTime = startTime;

    int fd = ::open(dst, O_WRONLY);

    while (copiedSize < fileSize) {
        if (taskCancelAbles[dst]) {
            auto info = copyFileInfoCreate(
                dst,
                CopyFileState_canceled,
                (double)copiedSize / fileSize,
                0,
                0,
                nullptr
            );
            auto message = nativeMessageCreate(
                NativeEvent_fileCopy,
                {.as_ptr = info},
                0,
                (CopyPtrValue)copyFileInfoCopy,
                (FreePtrValue)copyFileInfoDestroy
            );
            MK::Utils::sendNativeMessage(message, callbackPluginId);

            srcFile.close();
            dstFile.close();

            std::error_code errCode;
            std::filesystem::remove(dst, errCode);
            MK::LOG_I("{}", "Copy file canceled!");
            return;
        }

        srcFile.read(buffer.data(), buffer.size());
        auto bytesRead = srcFile.gcount();
        if (bytesRead <= 0) {
            if (srcFile.eof()) break;
            auto info = copyFileInfoCreate(
                dst,
                CopyFileState_failed,
                (double)copiedSize / fileSize,
                0,
                0,
                "Read failure (maybe device or src file removed)!"
            );
            auto message = nativeMessageCreate(
                NativeEvent_fileCopy,
                {.as_ptr = info},
                0,
                (CopyPtrValue)copyFileInfoCopy,
                (FreePtrValue)copyFileInfoDestroy
            );
            MK::Utils::sendNativeMessage(message, callbackPluginId);
            return;
        }

        dstFile.write(buffer.data(), bytesRead);
        if (!dstFile) {
            auto info = copyFileInfoCreate(
                dst,
                CopyFileState_failed,
                (double)copiedSize / fileSize,
                0,
                0,
                "Write failure (maybe device or dst file removed)!"
            );
            auto message = nativeMessageCreate(
                NativeEvent_fileCopy,
                {.as_ptr = info},
                0,
                (CopyPtrValue)copyFileInfoCopy,
                (FreePtrValue)copyFileInfoDestroy
            );
            MK::Utils::sendNativeMessage(message, callbackPluginId);
            return;
        }
        dstFile.flush();
        if (fd >= 0) {
            fdatasync(fd);
        }

        copiedSize += bytesRead;

        auto now = std::chrono::steady_clock::now();

        // 每隔100ms，回调一次
        auto elapsedSinceLastCb = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastCallbackTime).count();
        if (elapsedSinceLastCb >= 100) {
            auto info = copyFileInfoCreate(
                dst,
                CopyFileState_copying,
                (double)copiedSize / fileSize,
                0,
                0,
                nullptr
            );
            auto message = nativeMessageCreate(
                NativeEvent_fileCopy,
                {.as_ptr = info},
                0,
                (CopyPtrValue)copyFileInfoCopy,
                (FreePtrValue)copyFileInfoDestroy
            );
            MK::Utils::sendNativeMessage(message, callbackPluginId);

            lastCallbackTime = now;
        }
    }

    if (copiedSize == fileSize) {
        auto info = copyFileInfoCreate(
            dst,
            CopyFileState_success,
            1,
            0,
            0,
            nullptr
        );
        auto message = nativeMessageCreate(
            NativeEvent_fileCopy,
            {.as_ptr = info},
            0,
            (CopyPtrValue)copyFileInfoCopy,
            (FreePtrValue)copyFileInfoDestroy
        );
        MK::Utils::sendNativeMessage(message, callbackPluginId);
    } else {
        auto info = copyFileInfoCreate(
            dst,
            CopyFileState_failed,
            (double)copiedSize / fileSize,
            0,
            0,
            "Copy interrupted!"
        );
        auto message = nativeMessageCreate(
            NativeEvent_fileCopy,
            {.as_ptr = info},
            0,
            (CopyPtrValue)copyFileInfoCopy,
            (FreePtrValue)copyFileInfoDestroy
        );
        MK::Utils::sendNativeMessage(message, callbackPluginId);
    }
    srcFile.close();
    dstFile.flush();
    dstFile.close();
    MK::LOG_I("{}", "finish copy file!");
}

CopyFileInfoRef
copyFileInfoCreate(
    const char *const dst,
    const CopyFileState state,
    const double progress,
    const int64_t speed,
    const int64_t remainingTime,
    const char *const error
) {
    auto info = new CopyFileInfo();
    if (dst) info->dst = strdup(dst);
    info->state = state;
    info->progress = progress;
    info->speed = speed;
    info->remainingTime = remainingTime;
    if (error) info->error = strdup(error);
    return info;
}

CopyFileInfoRef
copyFileInfoCopy(const CopyFileInfoRef info) {
    return copyFileInfoCreate(
        info->dst,
        info->state,
        info->progress,
        info->speed,
        info->remainingTime,
        info->error
    );
}

void
copyFileInfoDestroy(const CopyFileInfoRef info) {
    if (info->dst) free(info->dst);
    if (info->error) free(info->error);
    delete info;
}

bool
copyFile(
    const char *const src,
    const char *const dst,
    const char *const callbackPluginId
) {
    if (taskCancelAbles.find(dst) != taskCancelAbles.end()) {
        MK::LOG_W("{}", "Task already exists!");
        return false;
    }
    taskCancelAbles[dst] = false;
    auto srcStr = std::make_shared<const std::string>(src);
    auto dstStr = std::make_shared<const std::string>(dst);
    auto callbackPluginIdStr = std::make_shared<const std::string>(callbackPluginId ? callbackPluginId : "");
    std::thread(
        [](
            std::shared_ptr<const std::string> src,
            std::shared_ptr<const std::string> dst,
            std::shared_ptr<const std::string> callbackPluginId
        ) {
            copyFileImpl(
                src->c_str(),
                dst->c_str(),
                callbackPluginId->c_str()
            );
            taskCancelAbles.erase(dst->c_str());
        },
        srcStr,
        dstStr,
        callbackPluginIdStr
    )
        .detach();
    return true;
}

void
cancelTask(const char *const dst) {
    if (taskCancelAbles.find(dst) == taskCancelAbles.end()) {
        MK::LOG_W("{}", "Task not found!");
        return;
    }
    taskCancelAbles[dst] = true;
}

TransferDataRef
transferDataCreate(
    const char *const id,
    const uint8_t *const data,
    const int64_t len,
    const int64_t stride,
    const int64_t height
) {
    auto dataRef = new TransferData();
    if (id) dataRef->id = strdup(id);
    if (data) {
        try {
            dataRef->value = new uint8_t[len];
            memcpy(dataRef->value, data, len);
            dataRef->len = len;
            dataRef->stride = stride;
            dataRef->height = height;
        } catch (std::exception e) {
            MK::LOG_E("{}", e.what());
        }
    }
    return dataRef;
}

TransferDataRef
transferDataCopy(const TransferDataRef data) {
    return transferDataCreate(
        data->id,
        data->value,
        data->len,
        data->stride,
        data->height
    );
}

void
transferDataDestroy(const TransferDataRef data) {
    if (data->id) free(data->id);
    if (data->value) delete[] data->value;
    delete data;
}

void
transferData(
    const char *const pluginId,
    const char *const id,
    const uint8_t *const data,
    const int64_t len,
    const int64_t stride,
    const int64_t height
) {
    auto transferData = transferDataCreate(id, data, len, stride, height);
    auto message = nativeMessageCreate(
        NativeEvent_dataTransfer,
        {.as_ptr = transferData},
        0,
        (CopyPtrValue)transferDataCopy,
        (FreePtrValue)transferDataDestroy
    );
    MK::Utils::sendNativeMessage(message, pluginId);
}
