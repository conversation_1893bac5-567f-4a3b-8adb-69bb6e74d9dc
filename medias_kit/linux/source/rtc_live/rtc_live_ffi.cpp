#include "rtc_live/rtc_live_ffi.h"

#include "rtc_live/rtc_live.hpp"
#include "utils/logger.hpp"

RtcStatsRef
rtcStatsCreate(
    const int userCount,
    const int sendVideoBitrate,
    const int sendLossRate,
    const int cpuTotalUsage,
    const int memoryTotalUsage,
    const int gatewayRtt
) {
    auto *stats = new RtcStats{
        userCount,
        sendVideoBitrate,
        sendLossRate,
        cpuTotalUsage,
        memoryTotalUsage,
        gatewayRtt};
    return stats;
}

RtcStatsRef
rtcStatsCopy(const RtcStatsRef stats) {
    return rtcStatsCreate(
        stats->userCount,
        stats->sendVideoBitrate,
        stats->sendLossRate,
        stats->cpuTotalUsage,
        stats->memoryTotalUsage,
        stats->gatewayRtt
    );
}

void
rtcStatsDestroy(const RtcStatsRef stats) {
    delete stats;
}

RtcLiveRef
rtcLiveCreate(
    const char *const streamType,
    const char *const appId,
    const char *const token,
    const char *const channelId,
    const int userId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const char *const audioRenderName,
    const int audioRenderVolume,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels
) {
    auto rtcLive = new MK::RtcLive(
        std::make_shared<const std::string>(streamType),
        std::make_shared<const std::string>(appId),
        std::make_shared<const std::string>(token),
        std::make_shared<const std::string>(channelId),
        std::make_shared<const std::string>(std::to_string(userId)),
        videoCaptureDevice,
        std::make_shared<const std::string>(audioCaptureName),
        std::make_shared<const std::string>(audioRenderName),
        audioRenderVolume / 100.0,
        width,
        height,
        framerate,
        bitrate,
        sampleRate,
        numChannels
    );
    return rtcLive;
}

void
rtcLiveDestroy(RtcLiveRef rtcLive) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    delete rtcLivePtr;
}

bool
rtcLiveInit(RtcLiveRef rtcLive) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    return rtcLivePtr->init();
}

void
attachRemoteAudioHandler(
    RtcLiveRef rtcLive,
    RecorderRef recorder,
    RtmpLiveRef rtmpLive
) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->attachRemoteAudioHandler(
        [=](char *frame, int frameLen) {
            if (recorder) {
                recorderRecordRemoteAudio(recorder, frame, frameLen);
            }
            if (rtmpLive) {
                rtmpLivePushRtcRemoteAudio(rtmpLive, frame, frameLen);
            }
        }
    );
}

void
rtcLiveStart(RtcLiveRef rtcLive) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->start();
}

void
rtcLiveStop(RtcLiveRef rtcLive) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->stop();
}

void
rtcLiveSpeak(RtcLiveRef rtcLive, bool enable) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->speak(enable);
}

void
rtcLiveChangeAudioSource(RtcLiveRef rtcLive, const char *const audioCaptureName) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->updateAudioCapture(
        std::make_shared<const std::string>(audioCaptureName)
    );
}

void
rtcLiveChangeAudioSink(
    RtcLiveRef rtcLive,
    const char *const audioRenderName,
    const double volume
) {
    assert(rtcLive);
    auto *rtcLivePtr = static_cast<MK::RtcLive *>(rtcLive);
    rtcLivePtr->updateAudioRender(
        std::make_shared<const std::string>(audioRenderName),
        volume
    );
}
