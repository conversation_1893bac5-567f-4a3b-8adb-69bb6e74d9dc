#include "rtc_live/rtc_live.hpp"

#include "NGIAgoraMediaNode.h"
#include "NGIAgoraMediaNodeFactory.h"
#include "NGIAgoraVideoTrack.h"
#include "rtc_live/rtc_live_ffi.h"
#include "stream&capture/local_stream.hpp"
#include "utils/logger.hpp"
#include "utils/utils.hpp"
#include <mutex>

namespace MK {

RtcLive::RtcLive(
    const std::shared_ptr<const std::string> streamType,
    const std::shared_ptr<const std::string> appId,
    const std::shared_ptr<const std::string> token,
    const std::shared_ptr<const std::string> channelId,
    const std::shared_ptr<const std::string> userId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const std::shared_ptr<const std::string> audioCaptureName,
    const std::shared_ptr<const std::string> audioRenderName,
    const double audioRenderVolume,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels
)
    : audioRenderer(
          new AudioRenderer(
              audioRenderName,
              audioRenderVolume
          )
      ),
      localAudioStream(
          new LocalAudioStream(
              this,
              audioCaptureName,
              sampleRate,
              numChannels
          )
      ),
      localVideoStream(
          new LocalVideoStream(
              this,
              videoCaptureDevice,
              width,
              height,
              framerate,
              bitrate
          )
      ),
      streamType(streamType),
      appId(appId),
      token(token),
      channelId(channelId),
      userId(userId) {}

RtcLive::~RtcLive() {
    pause = true;
    localVideoStream.reset();
    localAudioStream.reset();
    if (connection) {
        connection->getLocalUser()->unregisterAudioFrameObserver(this);
        connection->unregisterObserver(this);
        connection->unregisterNetworkObserver(this);
        connection->disconnect();
        factory = nullptr;
        connection = nullptr;
    }
    if (audioRenderer) {
        delete audioRenderer;
    }
    if (service) {
        service->release();
    }
    LOG_I("{}", "Memory free!");
}

bool
RtcLive::init() {
    if (isInit) {
        return isInit;
    }
    // 创建服务
    service = createAgoraService();
    if (!service) {
        LOG_E("{}", "createAgoraService failure!");
        return isInit;
    }

    // 服务配置
    agora::base::AgoraServiceConfiguration serviceConfig;
    const char *home = std::getenv("HOME");
    auto baseDir = std::string(home) + "/Documents/v202310/agora.logs/rtc_" +
                   *streamType + "/";

    serviceConfig.appId = appId->c_str();
    serviceConfig.logConfig.filePath = baseDir.c_str();
    serviceConfig.enableAudioProcessor = true;
    serviceConfig.enableAudioDevice = false;
    serviceConfig.domainLimit = true;

    // 初始化服务
    if (agora::ERR_OK != service->initialize(serviceConfig)) {
        LOG_E("{}", "service initialize failure!");
        return isInit;
    }

    // 创建媒体节点工厂
    factory = service->createMediaNodeFactory();
    if (!factory) {
        LOG_E("{}", "service createMediaNodeFactory failure!");
        return isInit;
    }

    // 连接配置
    agora::rtc::RtcConnectionConfiguration connectConfig;
    connectConfig.autoSubscribeVideo = false;
    connectConfig.autoSubscribeAudio = false;
    connectConfig.clientRoleType = agora::rtc::CLIENT_ROLE_BROADCASTER;
    connectConfig.channelProfile = agora::CHANNEL_PROFILE_LIVE_BROADCASTING;
    connectConfig.maxSendBitrate = 5000;

    // 创建连接
    connection = service->createRtcConnection(connectConfig);
    if (!connection) {
        LOG_E("{}", "service createRtcConnection failure!");
        return isInit;
    }

    // 强制硬编码, 影响画质, 与美乐威GPU编码器冲突(有概率导致编码器初始化失败)
    // auto agoraParameter = connection->getAgoraParameter();
    // agoraParameter->setBool(KEY_RTC_VIDEO_ENABLED_HW_ENCODER, true);
    // agoraParameter->setString(KEY_RTC_VIDEO_HW_ENCODER_PROVIDER, "nv");

    // 注册连接状态观察者
    auto ret = connection->registerObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 注册网络状态观察者
    ret = connection->registerNetworkObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerNetworkObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 注册用户状态观察者
    ret = connection->getLocalUser()->registerLocalUserObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerLocalUserObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 连接参数设置
    agora::rtc::TConnectSettings settings{
        .token = token->c_str(),
        .channelId = channelId->c_str(),
        .userId = userId->c_str(),
    };

    // 开始连接
    ret = connection->connect(settings);
    if (agora::ERR_OK != ret) {
        LOG_E("connection connect failure! errcode: {}", ret);
        return isInit;
    }

    if (*streamType == "main") {
        // 初始化音频渲染器
        if (!audioRenderer->init()) {
            LOG_E("{}", "audioRenderer init failure!");
            return isInit;
        }
    }

    // 订阅远端所有音频流
    // ret = connection->getLocalUser()->subscribeAllAudio();
    // if (agora::ERR_OK != ret) {
    //     LOG_E("connection subscribeAllAudio failure! errcode: {}", ret);
    //     return isInit;
    // }

    ret = connection->getLocalUser()->setPlaybackAudioFrameBeforeMixingParameters(
        1, 48000
    );
    if (agora::ERR_OK != ret) {
        LOG_E(
            "connection setPlaybackAudioFrameBeforeMixingParameters failure! errcode: {}",
            ret
        );
        return isInit;
    }

    ret = connection->getLocalUser()->setPlaybackAudioFrameParameters(
        1, 48000
    );
    if (agora::ERR_OK != ret) {
        LOG_E(
            "connection setPlaybackAudioFrameParameters failure! errcode: {}",
            ret
        );
        return isInit;
    }

    // 注册音频流观察者
    ret = connection->getLocalUser()->registerAudioFrameObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E(
            "connection registerAudioFrameObserver failure! errcode: {}",
            ret
        );
        return isInit;
    }

    // 初始化本地主音频流
    if (!localAudioStream->init()) {
        LOG_E("{}", "mainLocalAudioStream init failure!");
        return isInit;
    }

    // 初始化本地主视频流
    if (!localVideoStream->init()) {
        LOG_E("{}", "mainLocalVideoStream init failure!");
        return isInit;
    }

    pause = false;
    return isInit = true;
}

void
RtcLive::attachRemoteAudioHandler(CallbackAudio callbackAudio) {
    std::lock_guard<std::mutex> lock(callbackMtx);
    this->callbackAudio = callbackAudio;
}

void
RtcLive::start() {
    pause = false;
    LOG_I("{}", "start rtc live");
}

void
RtcLive::stop() {
    pause = true;
    LOG_I("{}", "stop rtc live");
}

void
RtcLive::speak(bool enable) {
    enableSpeak = enable;
    LOG_I("speak enabled {}", enable);
}

bool
RtcLive::updateAudioCapture(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    if (!localAudioStream->capture->init(audioCaptureName)) {
        LOG_E("{}", "audioCapture init failure!");
        return false;
    }
    return true;
}

bool
RtcLive::updateAudioRender(
    const std::shared_ptr<const std::string> audioRenderName,
    const double volume
) {
    audioRenderer->setVolume(volume);
    if (!audioRenderer->init(audioRenderName)) {
        LOG_E("{}", "audioRenderer init failure!");
        return false;
    }
    return true;
}

#pragma mark-- ILocalVideoStreamController

agora::agora_refptr<agora::rtc::IVideoFrameSender>
RtcLive::createVideoFrameSender() const {
    return factory->createVideoFrameSender();
}

agora::agora_refptr<agora::rtc::ILocalVideoTrack>
RtcLive::createCustomVideoTrack(
    agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
) const {
    return service->createCustomVideoTrack(sender);
}

int
RtcLive::publishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getLocalUser()->publishVideo(videoTrack);
}

int
RtcLive::unpublishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getLocalUser()->unpublishVideo(videoTrack);
}

bool
RtcLive::canCommitVideo() const {
    return connected && !pause;
}

bool
RtcLive::videoHandler(
    const PixelFormat pixelFormat,
    const void *frame,
    const size_t frameLen,
    const size_t width,
    const size_t height
) const {
    return false;
}

#pragma mark-- ILocalAudioStreamController

agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
RtcLive::createAudioPcmDataSender() const {
    return factory->createAudioPcmDataSender();
}

agora::agora_refptr<agora::rtc::ILocalAudioTrack>
RtcLive::createCustomAudioTrack(
    agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
) const {
    return service->createCustomAudioTrack(sender);
}

int
RtcLive::publishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getLocalUser()->publishAudio(audioTrack);
}

int
RtcLive::unpublishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getLocalUser()->unpublishAudio(audioTrack);
}

AudioCommitType
RtcLive::canCommitAudio() const {
    if (connected && enableSpeak && !pause) {
        return AudioCommitType::local;
    }
    return AudioCommitType::disable;
}

const std::shared_ptr<const std::vector<char>>
RtcLive::audioHandler(const char *frame, size_t frameLen) {
    return std::make_shared<std::vector<char>>();
}

#pragma mark-- ILocalUserObserver

void
RtcLive::onUserInfoUpdated(
    agora::user_id_t userId,
    USER_MEDIA_INFO msg,
    bool val
) {
    LOG_I("userId: {}, msg: {}, val: {},", userId, msg, val);
    if (*streamType == "main") {
        agora::rtc::UserInfo userInfo;
        connection->getUserInfoByUserAccount(userId, &userInfo);
        if (USER_MEDIA_INFO_MUTE_AUDIO == msg && userInfo.uid / 10000 != std::stoi(*this->userId)) {
            if (val) {
                connection->getLocalUser()->unsubscribeAudio(userId);
            } else {
                auto ret = connection->getLocalUser()->subscribeAudio(userId);
                if (agora::ERR_OK == ret) {
                    LOG_I("subscribeAudio success, target userId: {}, self userId: {}", userId, *this->userId);
                } else {
                    LOG_E("subscribeAudio failure, userId: {}  ret: {}", userId, ret);
                }
            }
        }
    }
}

void
RtcLive::onRemoteAudioTrackStatistics(
    agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack,
    const agora::rtc::RemoteAudioTrackStats &stats
) {}

void
RtcLive::onUserAudioTrackSubscribed(
    agora::user_id_t userId,
    agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack
) {}

void
RtcLive::onUserAudioTrackStateChanged(
    agora::user_id_t userId,
    agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack,
    agora::rtc::REMOTE_AUDIO_STATE state,
    agora::rtc::REMOTE_AUDIO_STATE_REASON reason,
    int elapsed
) {
    LOG_I("userId: {}, state: {}, reason: {}, elapsed: {}", userId, state, reason, elapsed);
}

#pragma mark-- IAudioFrameObserverBase

bool
RtcLive::onPlaybackAudioFrameBeforeMixing(
    const char *channelId,
    agora::media::base::user_id_t userId,
    AudioFrame &audioFrame
) {
    // agora::rtc::UserInfo userInfo;
    // connection->getUserInfoByUserAccount(userId, &userInfo);

    // auto size = audioFrame.samplesPerChannel * audioFrame.channels *
    //             audioFrame.bytesPerSample;
    // audioRenderer->pushFrame(
    //     std::make_shared<AudioRenderer::AudioFrame>(
    //         (char *)audioFrame.buffer, size
    //     )
    // );
    // callbackAudio((char *)audioFrame.buffer, size);
    return true;
}

bool
RtcLive::onPlaybackAudioFrame(
    const char *channelId, AudioFrame &audioFrame
) {
    auto size = audioFrame.samplesPerChannel * audioFrame.channels * audioFrame.bytesPerSample;
    audioRenderer->pushFrame(
        std::make_shared<AudioRenderer::AudioFrame>(
            (char *)audioFrame.buffer, size
        )
    );
    std::lock_guard<std::mutex> lock(callbackMtx);
    if (callbackAudio) {
        callbackAudio((char *)audioFrame.buffer, size);
    }
    return true;
}

#pragma mark-- IRtcConnectionObserver

void
RtcLive::onConnecting(
    const agora::rtc::TConnectionInfo &connectionInfo,
    agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
) {
    connected = false;
    LOG_I("Reason: {}", reason);
}

void
RtcLive::onConnected(
    const agora::rtc::TConnectionInfo &connectionInfo,
    agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
) {
    connected = true;
    LOG_I("Reason: {}", reason);
}

void
RtcLive::onReconnecting(
    const agora::rtc::TConnectionInfo &connectionInfo,
    agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
) {
    connected = false;
    LOG_I("Reason: {}", reason);
}

void
RtcLive::onReconnected(
    const agora::rtc::TConnectionInfo &connectionInfo,
    agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
) {
    connected = true;
    LOG_I("Reason: {}", reason);
}

void
RtcLive::onConnectionLost(
    const agora::rtc::TConnectionInfo &connectionInfo
) {
    connected = false;
    LOG_I("Info: {}", connectionInfo.state);
}

void
RtcLive::onDisconnected(
    const agora::rtc::TConnectionInfo &connectionInfo,
    agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
) {
    connected = false;
    LOG_I("Reason: {}", reason);

    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveReInit,
            {0},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

void
RtcLive::onTokenPrivilegeWillExpire(const char *token) {
    LOG_W("{}", "Token will expire");
}

void
RtcLive::onTokenPrivilegeDidExpire() {
    LOG_W("{}", "Token expire");
    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveReInit,
            {0},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

void
RtcLive::onUserJoined(agora::user_id_t userId) {
    agora::rtc::UserInfo userInfo;
    connection->getUserInfoByUserAccount(userId, &userInfo);
    auto uid = userInfo.uid / 10000;
    LOG_I("[{}] User joined", uid);

    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveUserJoined,
            {.as_int = uid},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

void
RtcLive::onUserLeft(
    agora::user_id_t userId, agora::rtc::USER_OFFLINE_REASON_TYPE reason
) {
    agora::rtc::UserInfo userInfo;
    connection->getUserInfoByUserAccount(userId, &userInfo);
    LOG_I("[{}] User left", userInfo.uid);

    int64_t value = reason;
    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveUserLeft,
            {.as_int = userInfo.uid | (value << 32)},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

void
RtcLive::onTransportStats(const agora::rtc::RtcStats &stats) {
    auto newStats = rtcStatsCreate(
        stats.userCount,
        stats.txVideoKBitRate,
        stats.txPacketLossRate,
        stats.cpuTotalUsage,
        stats.memoryTotalUsageRatio,
        stats.gatewayRtt
    );
    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveTransportStats,
            {.as_ptr = newStats},
            (int64_t)this,
            (CopyPtrValue)rtcStatsCopy,
            (FreePtrValue)rtcStatsDestroy
        )
    );
}

void
RtcLive::onUserNetworkQuality(
    agora::user_id_t userId,
    agora::rtc::QUALITY_TYPE txQuality,
    agora::rtc::QUALITY_TYPE rxQuality
) {

    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveNetworkQuality,
            {.as_int = txQuality},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

void
RtcLive::onNetworkTypeChanged(agora::rtc::NETWORK_TYPE type) {
    Utils::sendNativeMessage(
        nativeMessageCreate(
            NativeEvent_rtcLiveNetworkType,
            {.as_int = type},
            (int64_t)this,
            nullptr,
            nullptr
        )
    );
}

} // namespace MK
