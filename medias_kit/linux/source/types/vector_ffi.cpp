
#include "types/vector_ffi.h"

#include <vector>

#include "utils/logger.hpp"

VectorRef
vectorCreate() {
    return new std::vector<void *>();
}

void
vectorDestroy(VectorRef vector, FreeElement freeElement) {
    assert(vector);
    auto *vectorPtr = static_cast<std::vector<void *> *>(vector);
    if (freeElement) {
        for (auto element : *vectorPtr) {
            freeElement(element);
        }
    }
    delete vectorPtr;
}

void
vectorDestroyWithoutFreeElement(VectorRef vector) {
    assert(vector);
    delete static_cast<std::vector<void *> *>(vector);
}

int
vectorSize(VectorRef vector) {
    assert(vector);
    return static_cast<std::vector<void *> *>(vector)->size();
}

void *
vectorElementAt(VectorRef vector, int index) {
    assert(vector);
    return static_cast<std::vector<void *> *>(vector)->at(index);
}
