#include "types/types_ffi.h"

#include "utils/logger.hpp"
#include <cassert>

NativeMessageRef
nativeMessageCreate(
    const NativeEvent event,
    const NativeValue value,
    const int64_t extra,
    const CopyPtrValue copyPtr,
    const FreePtrValue freePtr
) {
    assert((copyPtr && freePtr) || (!copyPtr && !freePtr));
    auto *message = new NativeMessage();
    message->event = event;
    message->value = value;
    message->extra = extra;
    message->_copyPtr = copyPtr;
    message->_freePtr = freePtr;
    return message;
}

NativeMessageRef
nativeMessageCopy(const NativeMessageRef message) {
    assert(message);
    auto value = message->value;
    if (message->_copyPtr) {
        value.as_ptr = message->_copyPtr(message->value.as_ptr);
    }
    return nativeMessageCreate(
        message->event,
        value,
        message->extra,
        message->_copyPtr,
        message->_freePtr
    );
}

void
nativeMessageDestroy(const NativeMessageRef message) {
    assert(message);
    if (message->_freePtr) {
        message->_freePtr(message->value.as_ptr);
    }
    delete message;
}

void
nativeMessageDestroyWithoutValuePtr(const NativeMessageRef message) {
    assert(message);
    delete message;
}
