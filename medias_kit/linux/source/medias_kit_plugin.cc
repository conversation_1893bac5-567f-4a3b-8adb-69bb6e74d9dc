#include "medias_kit/medias_kit_plugin.h"

#include <flutter_linux/flutter_linux.h>
#include <libavcodec/avcodec.h>

#include <cstdint>
#include <cstdio>
#include <map>
#include <mutex>
#include <shared_mutex>
#include <string>

#include "medias_kit_plugin_private.hpp"
#include "utils/logger.hpp"
#include "utils/module.hpp"
#include "utils/utils.hpp"

#define MEDIAS_KIT_PLUGIN(obj) \
    (G_TYPE_CHECK_INSTANCE_CAST(obj, medias_kit_plugin_get_type(), MediasKitPlugin))

/**
 * @brief 插件列表共享锁
 */
std::shared_mutex pluginsSharedMutex;

/**
 * @brief 插件列表
 */
std::map<const std::string, const MediasKitPlugin *> plugins;

G_DEFINE_TYPE(MediasKitPlugin, medias_kit_plugin, g_object_get_type())

// Called when a method call is received from Flutter.
static void
medias_kit_plugin_handle_method_call(MediasKitPlugin *self, FlMethodCall *method_call) {
    g_autoptr(FlMethodResponse) response = nullptr;
    const gchar *method = fl_method_call_get_name(method_call);
    auto argument = std::make_shared<const MK::Argument>(fl_method_call_get_args(method_call));
    auto module = MK::Module::parse(method);
    if (module->name == "Core") {
        response = MK::coreMessageHandler(
            *self,
            (CoreEvent)module->event,
            argument
        );
    } else {
        response = FL_METHOD_RESPONSE(fl_method_not_implemented_response_new());
    }
    fl_method_call_respond(method_call, response, nullptr);
}

static void
medias_kit_plugin_dispose(GObject *object) {
    std::unique_lock<std::shared_mutex> writeLock(pluginsSharedMutex);
    plugins.erase(std::to_string((intptr_t)object));
    G_OBJECT_CLASS(medias_kit_plugin_parent_class)->dispose(object);
}

static void
medias_kit_plugin_class_init(MediasKitPluginClass *klass) {
    G_OBJECT_CLASS(klass)->dispose = medias_kit_plugin_dispose;
}

static void
medias_kit_plugin_init(MediasKitPlugin *self) {
    std::unique_lock<std::shared_mutex> writeLock(pluginsSharedMutex);
    plugins.insert_or_assign(std::to_string((intptr_t)self), self);

    const AVCodec *codec = avcodec_find_encoder_by_name("h264_nvenc");
    if (!codec) {
        fprintf(stderr, "Codec h264_nvenc not found\n");
    }
}

static void
method_call_cb(FlMethodChannel *channel, FlMethodCall *method_call, gpointer user_data) {
    MediasKitPlugin *plugin = MEDIAS_KIT_PLUGIN(user_data);
    medias_kit_plugin_handle_method_call(plugin, method_call);
}

void
medias_kit_plugin_register_with_registrar(FlPluginRegistrar *registrar) {
    MediasKitPlugin *plugin = MEDIAS_KIT_PLUGIN(
        g_object_new(
            medias_kit_plugin_get_type(),
            nullptr
        )
    );
    g_autoptr(FlStandardMethodCodec) codec = fl_standard_method_codec_new();

    // g_autoptr(FlMethodChannel)
    plugin->channel = fl_method_channel_new(
        fl_plugin_registrar_get_messenger(registrar),
        "medias_kit",
        FL_METHOD_CODEC(codec)
    );

    fl_method_channel_set_method_call_handler(
        plugin->channel,
        method_call_cb,
        g_object_ref(plugin),
        g_object_unref
    );

    plugin->textureRegistrar = fl_plugin_registrar_get_texture_registrar(registrar);
    plugin->view = fl_plugin_registrar_get_view(registrar);

    g_object_unref(plugin);
}

void
MK::findPlugin(
    const std::string &pluginId,
    const FindPluginCallback callback
) {
    std::shared_lock<std::shared_mutex> readLock(pluginsSharedMutex);
    callback(pluginId, *plugins[pluginId]);
}

void
MK::enumeratePlugins(const FindPluginCallback callback) {
    std::shared_lock<std::shared_mutex> readLock(pluginsSharedMutex);
    for (auto &plugin : plugins) {
        callback(plugin.first, *plugin.second);
    }
}

FlMethodResponse *
MK::coreMessageHandler(
    const MediasKitPlugin &plugin,
    const CoreEvent event,
    const std::shared_ptr<const Argument> argument
) {
    g_autoptr(FlValue) result = fl_value_new_null();
    switch (event) {
    case CoreEvent_bindNativePlugin: {
        result = fl_value_new_string(std::to_string((intptr_t)&plugin).c_str());
    } break;
    case CoreEvent_bindDartApiDL: {
        auto &mutPlugin = const_cast<MediasKitPlugin &>(plugin);
        auto ret = Dart_InitializeApiDL((void *)argument->getInt("apiPointer"));
        if (ret != 0) {
            return FL_METHOD_RESPONSE(
                fl_method_error_response_new(
                    std::to_string(ret).c_str(),
                    "Dart_InitializeApiDL failure!",
                    nullptr
                )
            );
        }
        mutPlugin.sendPort = argument->getInt("sendPort");
    } break;
    }
    return FL_METHOD_RESPONSE(fl_method_success_response_new(result));
}
