#include "voice_helper/voice_helper_ffi.h"

#include "voice_helper/voice_helper.hpp"

VoiceCommandRef
voiceCommandCreate(
    const VoiceCommandState state,
    const char *const content
) {
    auto command = new VoiceCommand();
    command->state = state;
    if (content) command->content = strdup(content);
    return command;
}

VoiceCommandRef
voiceCommandCopy(const VoiceCommandRef command) {
    return voiceCommandCreate(command->state, command->content);
}

void
voiceCommandDestroy(const VoiceCommandRef command) {
    if (command->content) free(command->content);
    delete command;
}

bool
voiceHelperPrepare(
    const char *const bundlePath,
    const char *const audioCaptureName,
    const char *const audioRenderName,
    const double volume
) {
    return MK::VoiceHelper::share().init(
               std::make_shared<const std::string>(bundlePath)
           ) &&
           MK::VoiceHelper::share().waiting4awakening(
               std::make_shared<const std::string>(audioCaptureName)
           ) &&
           MK::VoiceHelper::share().waiting4speaking(
               std::make_shared<const std::string>(audioRenderName),
               volume
           ) &&
           MK::VoiceHelper::share().waiting4onlineCommand(
               std::make_shared<const std::string>(audioCaptureName)
           );
}

void
voiceHelperDisable() {
    MK::VoiceHelper::share().disable();
}

bool
voiceHelperSpeaking(const char *const text) {
    return MK::VoiceHelper::share().speakingText(
        std::make_shared<const std::string>(text)
    );
}

void
voiceHelperWaitingCommand() {
    return MK::VoiceHelper::share().startRecognize();
}
