#include "voice_helper/voice_helper.hpp"

#include <cassert>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <fstream>
#include <memory>
#include <string>
#include <system_error>
#include <unistd.h>
#include <vector>

#include "device/device_ffi.h"
#include "msc/msp_cmn.h"
#include "msc/msp_errors.h"
#include "msc/qisr.h"
#include "types/types_ffi.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"
#include "voice_helper/voice_helper_ffi.h"

namespace MK {

using namespace AIKIT;

// 采样率转换函数
void
resample16000To48000(const std::vector<short> &input, std::vector<short> &output) {
    if (input.empty()) {
        throw std::invalid_argument("Input data is empty.");
    }

    // 计算输出样本数
    size_t inputSamples = input.size();
    size_t requiredOutputSamples = inputSamples * 3; // 48000 / 16000 = 3

    // 如果 `output` 容量不足，扩容；否则直接使用现有缓冲区
    if (output.capacity() < requiredOutputSamples) {
        output.reserve(requiredOutputSamples);
    }
    output.resize(requiredOutputSamples);

    // 进行线性插值
    for (size_t i = 0; i < inputSamples - 1; ++i) {
        short x0 = input[i];
        short x1 = input[i + 1];

        // 原点
        output[i * 3] = x0;

        // 插值点
        output[i * 3 + 1] = x0 + (x1 - x0) / 3;
        output[i * 3 + 2] = x0 + 2 * (x1 - x0) / 3;
    }

    // 处理最后一个样本
    short lastSample = input[inputSamples - 1];
    output[requiredOutputSamples - 3] = lastSample;
    output[requiredOutputSamples - 2] = lastSample;
    output[requiredOutputSamples - 1] = lastSample;
}

void
VoiceHelper::onOutput(
    AIKIT_HANDLE *handle,
    const AIKIT_OutputData *output
) {
    auto self = (VoiceHelper *)handle->usrContext;
    if (handle->abilityID == VoiceHelper::share().ivwAbility) {
        // 本地语音唤醒
        VoiceHelper::share().isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;
        usleep(10 * 1000);

        Utils::sendNativeMessage(
            nativeMessageCreate(
                NativeEvent_voiceHelperAwakened,
                {0},
                0,
                nullptr,
                nullptr
            )
        );
    } else if (handle->abilityID == VoiceHelper::share().aisoundAbility) {
        // 本地语音合成
        if (output->node->type == AIKIT_DataAudio) {
            if (output->node->len <= 0) {
                return;
            }
            auto len = output->node->len / 2;
            if (self->sourceDatas.capacity() < len) {
                self->sourceDatas.reserve(len);
            }
            self->sourceDatas.resize(len);
            memcpy(self->sourceDatas.data(), output->node->value, output->node->len);
            resample16000To48000(self->sourceDatas, self->renderDatas);
            VoiceHelper::share().audioRender->pushFrame(
                std::make_shared<AudioRenderer::AudioFrame>(
                    (char *)self->renderDatas.data(),
                    (int)self->renderDatas.size() << 1
                ),
                -1
            );
        }
    }
}

void
VoiceHelper::onEvent(
    AIKIT_HANDLE *handle,
    AIKIT_EVENT eventType,
    const AIKIT_OutputEvent *eventValue
) {
    // LOG_I("abilityID: {}, eventType: {}", handle->abilityID, eventType);
}

void
VoiceHelper::onError(
    AIKIT_HANDLE *handle,
    int32_t err,
    const char *desc
) {
    LOG_E("abilityID: {}, err: {}, desc: {}", handle->abilityID, err, desc);
}

VoiceHelper &
VoiceHelper::share() {
    static VoiceHelper instance;
    return instance;
}

bool
VoiceHelper::init(
    const std::shared_ptr<const std::string> bundlePath
) {
    if (!assetsDir) {
        assetsDir = std::make_shared<const std::string>(
            *bundlePath + "/data/flutter_assets/packages/medias_kit/assets"
        );
        std::string home = std::getenv("HOME");
        workDir = std::make_shared<const std::string>(home + "/Documents/v202310/xfyun");
    }

    if (isInit) return isInit;

    LOG_I(
        "AIKIT 版本: {}, MSC 版本: {}",
        AIKIT_GetVersion(),
        MSPGetVersion("ver_msc", nullptr)
    );

    auto resDir = *assetsDir + "/xfyun";
    auto logPath = *workDir + "/aikit.log";
    std::error_code errCode;
    if (!std::filesystem::exists(*workDir, errCode)) {
        std::filesystem::create_directory(*workDir, errCode);
    }
    AIKIT_Configurator::builder()
        .app()
        // .appID("18f0bc65")
        // .apiKey("284f85346b7eb6a61104d6b973a64945")
        // .apiSecret("MjkxZWFhNmNiMDZjZjBjNWIzMTQ5ZjRj")
        .appID("83e905ec")
        .apiKey("9ed49e2743b5392deb831e4a7b773f75")
        .apiSecret("OWNiYjZiZWVlY2QyZTEyZDNlMWYyMzc4")
        .resDir(resDir.c_str())
        .workDir(workDir->c_str())
        .cfgFile("");

    AIKIT_Configurator::builder()
        .auth()
        .authType(0)
        .ability(abilities.c_str());

    AIKIT_Configurator::builder()
        .log()
        .logLevel(LOG_LVL_ERROR)
        .logMode(std::string("Debug") == CURRENT_BUILD_TYPE ? LOG_STDOUT : LOG_FILE)
        .logPath(logPath.c_str());

    auto ret = AIKIT_Init();
    if (ret != 0) {
        LOG_E("AIKIT_Init failed, error number: {}", ret);
        return isInit = false;
    }

    return isInit = true;
}

void
VoiceHelper::disable() {
    isDisabled = true;
}

bool
VoiceHelper::waiting4awakening(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    isDisabled = false;
    if (!ivwAudioCapture) {
        // filePath 长度不能超过 127, 否则 SDK 会无法读取文件
        auto filePath = *assetsDir + "/voice_words.txt";
        if (std::string("Debug") == CURRENT_BUILD_TYPE) {
            filePath = std::string(getenv("XF_ASSETS")) + "/voice_words.txt";
        }

        AIKIT_Callbacks callbacks = {onOutput, onEvent, onError};
        auto ret = AIKIT_RegisterAbilityCallback(ivwAbility.c_str(), callbacks);
        if (ret != 0) {
            LOG_E(
                "AIKIT_RegisterAbilityCallback failed, error number: {}, ability: {}",
                ret,
                ivwAbility
            );
            return false;
        }

        ret = AIKIT_EngineInit(ivwAbility.c_str(), nullptr);
        if (ret != 0) {
            LOG_E(
                "AIKIT_EngineInit failed, error number: {}, ability: {}",
                ret,
                ivwAbility
            );
            return false;
        }

        AIKIT_CustomBuilder *customBuilder = AIKIT_CustomBuilder::create();
        customBuilder->textPath("key_word", filePath.c_str(), 0);
        ret = AIKIT_LoadData(ivwAbility.c_str(), AIKIT_Builder::build(customBuilder));
        if (ret != 0) {
            LOG_E(
                "AIKIT_LoadData failed, error number: {}, ability: {}",
                ret,
                ivwAbility
            );
            delete customBuilder;
            return false;
        }
        delete customBuilder;

        int indices[] = {0};
        ret = AIKIT_SpecifyDataSet(ivwAbility.c_str(), "key_word", indices, 1);
        if (ret != 0) {
            LOG_E(
                "AIKIT_SpecifyDataSet failed, error number: {}, ability: {}",
                ret,
                ivwAbility
            );
            return false;
        }

        auto paramBuilder = AIKIT_ParamBuilder::create();
        paramBuilder->param("wdec_param_nCmThreshold", "0 0:1000", strlen("0 0:1000"));
        paramBuilder->param("gramLoad", true);
        ret = AIKIT_Start(
            ivwAbility.c_str(),
            AIKIT_Builder::build(paramBuilder),
            this,
            &ivwHandle
        );
        if (ret != 0) {
            LOG_E(
                "AIKIT_Start failed, error number: {}, ability: {}",
                ret,
                ivwAbility
            );
            delete paramBuilder;
            return false;
        }
        delete paramBuilder;

        ivwDataBuilder = AIKIT_DataBuilder::create();

        ivwAudioCapture = new AudioCapture(
            audioCaptureName,
            AiAudio::SAMPLE_RATE_16K,
            AiAudio::CHANNELS_1,
            [&](char *buffer, long bufferLen) {
                if (isDisabled) return;
                ivwDataBuilder->clear();
                auto audio = AiAudio::get(AiAudio::ENCODING_WAV)
                                 ->sampleRate(AiAudio::SAMPLE_RATE_16K)
                                 ->bitDepth(AiAudio::BIT_DEPTH_16)
                                 ->channels(AiAudio::CHANNELS_1)
                                 ->data(buffer, bufferLen)
                                 ->valid();
                if (!audio) {
                    LOG_E("{}", "Audio is invalid");
                    return;
                }
                ivwDataBuilder->payload(audio);
                auto ret = AIKIT_Write(
                    ivwHandle,
                    AIKIT_Builder::build(ivwDataBuilder)
                );
                if (ret != 0) {
                    LOG_E(
                        "AIKIT_Write failed, error number: {}, ability: {}",
                        ret,
                        ivwAbility
                    );
                }
            }
        );
        if (!ivwAudioCapture->init()) {
            LOG_E("{}", "audioCapture init failure!");
            return false;
        }
    } else {
        if (ivwAudioCapture->getCaptureName() == audioCaptureName) {
            return true;
        }
        if (!ivwAudioCapture->init(audioCaptureName)) {
            LOG_E("{}", "audioCapture init failure!");
            return false;
        }
    }
    LOG_I("{}", "waiting awakened!");
    return true;
}

bool
VoiceHelper::waiting4speaking(
    const std::shared_ptr<const std::string> audioRenderName,
    const double volume
) {
    if (!audioRender) {
        AIKIT_Callbacks callbacks = {onOutput, onEvent, onError};
        auto ret = AIKIT_RegisterAbilityCallback(aisoundAbility.c_str(), callbacks);
        if (ret != 0) {
            LOG_E(
                "AIKIT_RegisterAbilityCallback failed, error number: {}, ability: {}", ret, aisoundAbility
            );
            return false;
        }

        auto paramBuilder = AIKIT_ParamBuilder::create();
        // 必选参数，支持中文 xiaoyan 英文发音人 catherine
        paramBuilder->param("vcn", "xiaoyan", strlen("xiaoyan"));
        // 可选参数，语速 默认为50
        paramBuilder->param("speed", 50);
        // 可选参数，语调 默认为50
        paramBuilder->param("pitch", 50);
        // 可选参数，音量 默认为50
        paramBuilder->param("volume", 50);
        // 可选参数，文本编码格式，支持UTF-8 默认为GBK
        paramBuilder->param("textEncoding", "UTF-8", strlen("UTF-8"));
        ret = AIKIT_Start(
            aisoundAbility.c_str(),
            AIKIT_Builder::build(paramBuilder),
            this,
            &aisoundHandle
        );
        if (ret != 0) {
            LOG_E(
                "AIKIT_Start failed, error number: {}, ability: {}",
                ret,
                aisoundAbility
            );
            delete paramBuilder;
            return false;
        }
        delete paramBuilder;

        aisoundDataBuilder = AIKIT_DataBuilder::create();

        audioRender = new AudioRenderer(audioRenderName);
        audioRender->setVolume(volume);
        if (!audioRender->init()) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    } else {
        audioRender->setVolume(volume);
        if (!audioRender->init(audioRenderName)) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    }
    LOG_I("{}", "waiting speaking!");
    return true;
}

bool
VoiceHelper::speakingText(
    const std::shared_ptr<const std::string> text
) {
    if (!audioRender->init()) {
        return false;
    }
    aisoundDataBuilder->clear();
    auto textData = AiText::get("text")
                        ->data(text->c_str(), text->length())
                        ->valid();
    aisoundDataBuilder->payload(textData);
    auto ret = AIKIT_Write(
        aisoundHandle,
        AIKIT_Builder::build(aisoundDataBuilder)
    );
    if (ret != 0) {
        LOG_E(
            "AIKIT_Write failed, error number: {}, ability: {}",
            ret,
            aisoundAbility
        );
        return false;
    }
    return true;
}

bool
VoiceHelper::waiting4offlineCommand(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    if (!esrAudioCapture) {
        auto filePath = *assetsDir + "/cmd_fsa.txt";
        if (std::string("Debug") == CURRENT_BUILD_TYPE) {
            filePath = std::string(getenv("XF_ASSETS")) + "/cmd_fsa.txt";
        }

        AIKIT_Callbacks callbacks = {onOutput, onEvent, onError};
        auto ret = AIKIT_RegisterAbilityCallback(esrAbility.c_str(), callbacks);
        if (ret != 0) {
            LOG_E(
                "AIKIT_RegisterAbilityCallback failed, error number: {}, ability: {}",
                ret,
                esrAbility
            );
            return false;
        }

        auto paramBuilder = AIKIT_ParamBuilder::create();
        paramBuilder->param("decNetType", "fsa", strlen("fsa"));
        paramBuilder->param("punishCoefficient", 0.0);
        paramBuilder->param("wfst_addType", 0); // 0 中文 1英文
        ret = AIKIT_EngineInit(esrAbility.c_str(), paramBuilder->build());
        if (ret != 0) {
            LOG_E(
                "AIKIT_EngineInit failed, error number: {}, ability: {}",
                ret,
                esrAbility
            );
            delete paramBuilder;
            return false;
        }
        delete paramBuilder;

        auto customBuilder = AIKIT_CustomBuilder::create();
        customBuilder->textPath("FSA", filePath.c_str(), 0);
        ret = AIKIT_LoadData(esrAbility.c_str(), AIKIT_Builder::build(customBuilder));
        if (ret != 0) {
            LOG_E(
                "AIKIT_LoadData failed, error number: {}, ability: {}",
                ret,
                esrAbility
            );
            delete customBuilder;
            return false;
        }
        delete customBuilder;

        int indices[] = {0};
        ret = AIKIT_SpecifyDataSet(esrAbility.c_str(), "FSA", indices, 1);
        if (ret != 0) {
            LOG_E(
                "AIKIT_SpecifyDataSet failed, error number: {}, ability: {}",
                ret,
                esrAbility
            );
            return false;
        }

        paramBuilder = AIKIT_ParamBuilder::create();
        // 0 中文 1英文
        paramBuilder->param("languageType", 0);
        // vad子句之间的间隔时间,单位为10ms
        paramBuilder->param("vadEndGap", 75);
        // 是否开启vad
        paramBuilder->param("vadOn", true);
        // 解码门限
        paramBuilder->param("beamThreshold", 20);
        // 解码活动弧的数量限制
        paramBuilder->param("hisGramThreshold", 3000);
        // 后处理开关限制
        paramBuilder->param("postprocOn", false);
        paramBuilder->param("vadResponsetime", 1000);
        paramBuilder->param("vadLinkOn", true);
        // 单位为10ms，80为800ms，0.8s
        paramBuilder->param("vadSpeechEnd", 100);
        ret = AIKIT_Start(
            esrAbility.c_str(),
            AIKIT_Builder::build(paramBuilder),
            this,
            &esrHandle
        );
        if (ret != 0) {
            LOG_E(
                "AIKIT_Start failed, error number: {}, ability: {}",
                ret,
                esrAbility
            );
            delete paramBuilder;
            return false;
        }
        delete paramBuilder;

        esrDataBuilder = AIKIT_DataBuilder::create();

        esrAudioCapture = new AudioCapture(
            audioCaptureName,
            AiAudio::SAMPLE_RATE_16K,
            AiAudio::CHANNELS_1,
            [&](char *buffer, long bufferLen) {
                if (!isEnableEsr) {
                    return;
                }
                esrDataBuilder->clear();
                auto audio = AiAudio::get("audio")
                                 ->sampleRate(AiAudio::SAMPLE_RATE_16K)
                                 ->bitDepth(AiAudio::BIT_DEPTH_16)
                                 ->channels(AiAudio::CHANNELS_1)
                                 ->data(buffer, bufferLen)
                                 ->status(status)
                                 ->valid();
                if (!audio) {
                    LOG_E("{}", "Audio is invalid");
                    return;
                }
                esrDataBuilder->payload(audio);
                auto ret = AIKIT_Write(
                    esrHandle,
                    AIKIT_Builder::build(esrDataBuilder)
                );
                if (ret != 0) {
                    isEnableEsr = false;
                    LOG_E(
                        "AIKIT_Write failed, error number: {}, ability: {}",
                        ret,
                        esrAbility
                    );
                }

                AIKIT_OutputData *output = nullptr;
                ret = AIKIT_Read(esrHandle, &output);
                if (ret != 0) {
                    isEnableEsr = false;
                    LOG_E(
                        "AIKIT_Read failed, error number: {}, ability: {}",
                        ret,
                        esrAbility
                    );
                    return;
                }

                if (AIKIT_DataEnd == status) {
                    LOG_E("{}", ">>>>>>>>>>>>>>>> end");
                    isEnableEsr = false;
                }

                if (output) { // 获取输出结果
                    auto node = output->node;
                    while (node && node->value) {
                        LOG_I("key: {}, value: {}", node->key, (char *)node->value);
                        if (node->status == AIKIT_DataEnd) {
                            LOG_E("{}", ">>>>>>>>>>>>>>>> data end");
                            status = AIKIT_DataEnd;
                        }
                        node = node->next;
                    }
                }

                if (AIKIT_DataBegin == status) {
                    status = AIKIT_DataContinue;
                }
            }
        );
        if (!esrAudioCapture->init()) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    } else {
        if (esrAudioCapture->getCaptureName() == audioCaptureName) {
            return true;
        }
        if (!esrAudioCapture->init(audioCaptureName)) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    }
    LOG_I("{}", "waiting offline command!");
    return true;
}

bool
VoiceHelper::waiting4onlineCommand(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;
    if (!isrAudioCapture) {
        isrAudioCapture = new AudioCapture(
            audioCaptureName,
            AiAudio::SAMPLE_RATE_16K,
            AiAudio::CHANNELS_1,
            [&](char *buffer, long bufferLen) {
                int errorCode;
                if (MSP_AUDIO_SAMPLE_LAST == isrAudioStatus ||
                    MSP_AUDIO_SAMPLE_INIT == isrAudioStatus) {
                    if (isrSessionID) {
                        if (MSP_AUDIO_SAMPLE_LAST == isrAudioStatus) {
                            while (MSP_REC_STATUS_COMPLETE != recStatus) {
                                if (!getDeviceIsOnline()) {
                                    isLogin = false;
                                    break;
                                }
                                auto result = QISRGetResult(
                                    isrSessionID,
                                    &recStatus,
                                    0,
                                    &errorCode
                                );
                                if (result) {
                                    isrResult += result;

                                    Utils::sendNativeMessage(
                                        nativeMessageCreate(
                                            NativeEvent_voiceHelperRecognizing,
                                            {.as_ptr = strdup(isrResult.c_str())},
                                            0,
                                            [](void *ptr) {
                                                return (void *)strdup((char *)ptr);
                                            },
                                            [](void *ptr) {
                                                free(ptr);
                                            }
                                        )
                                    );
                                }
                                usleep(100 * 1000);
                            }

                            auto command = voiceCommandCreate(
                                isLogin ? VoiceCommandState_success : VoiceCommandState_disconnect,
                                isrResult.c_str()
                            );
                            Utils::sendNativeMessage(
                                nativeMessageCreate(
                                    NativeEvent_voiceHelperCommandParsed,
                                    {.as_ptr = command},
                                    0,
                                    (CopyPtrValue)voiceCommandCopy,
                                    (FreePtrValue)voiceCommandDestroy
                                )
                            );
                        }

                        QISRSessionEnd(
                            isrSessionID,
                            MSP_AUDIO_SAMPLE_INIT == isrAudioStatus ? "error" : "success"
                        );
                        isrSessionID = nullptr;
                        LOG_I("{}", "结束语音识别!");
                    }
                    isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;
                    return;
                }
                if (!isrSessionID) {
                    auto isrParams = "sub = iat, domain = iat, language = zh_cn, "
                                     "accent = mandarin, sample_rate = 16000, ptt = 0, "
                                     "result_type = plain, result_encoding = utf8";
                    isrSessionID = QISRSessionBegin(nullptr, isrParams, &errorCode);
                    if (MSP_SUCCESS != errorCode) {
                        LOG_E("QISRSessionBegin error! errorCode = {}", errorCode);
                        return;
                    }
                    isrResult = "";
                }

                auto ret = QISRAudioWrite(
                    isrSessionID,
                    buffer,
                    bufferLen,
                    isrAudioStatus,
                    &epStatus,
                    &recStatus
                );
                if (MSP_SUCCESS != ret) {
                    isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;
                    LOG_E("QISRAudioWrite error! ret = {}", ret);
                    return;
                }
                if (MSP_REC_STATUS_SUCCESS == recStatus) {
                    auto result = QISRGetResult(
                        isrSessionID,
                        &recStatus,
                        0,
                        &ret
                    );
                    if (MSP_SUCCESS != ret) {
                        LOG_E("QISRGetResult error! ret = {}", ret);
                    }
                    if (result) {
                        isrResult += result;

                        Utils::sendNativeMessage(
                            nativeMessageCreate(
                                NativeEvent_voiceHelperRecognizing,
                                {.as_ptr = strdup(isrResult.c_str())},
                                0,
                                [](void *ptr) {
                                    return (void *)strdup((char *)ptr);
                                },
                                [](void *ptr) {
                                    free(ptr);
                                }
                            )
                        );
                    }
                    if (MSP_REC_STATUS_COMPLETE == recStatus) {
                        isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;

                        auto command = voiceCommandCreate(
                            VoiceCommandState_success,
                            isrResult.c_str()
                        );
                        Utils::sendNativeMessage(
                            nativeMessageCreate(
                                NativeEvent_voiceHelperCommandParsed,
                                {.as_ptr = command},
                                0,
                                (CopyPtrValue)voiceCommandCopy,
                                (FreePtrValue)voiceCommandDestroy
                            )
                        );
                    }
                }
                if (MSP_EP_AFTER_SPEECH <= epStatus) {
                    isrAudioStatus = MSP_AUDIO_SAMPLE_LAST;
                }
            }
        );
        if (!isrAudioCapture->init()) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    } else {
        if (isrAudioCapture->getCaptureName() == audioCaptureName) {
            return true;
        }
        if (!isrAudioCapture->init(audioCaptureName)) {
            LOG_E("{}", "audioRender init failure!");
            return false;
        }
    }
    LOG_I("{}", "waiting online command!");
    return true;
}

void
VoiceHelper::startRecognize() {
    if (getDeviceIsOnline()) {
        if (isLogin) {
            isrAudioStatus = MSP_AUDIO_SAMPLE_FIRST;
        } else {
            isrAudioStatus = MSP_AUDIO_SAMPLE_INIT;
            usleep(10 * 1000);
            MSPLogout();

            std::string params = "appid = 83e905ec, work_dir = " + *workDir;
            auto ret = MSPLogin(nullptr, nullptr, params.c_str());
            isLogin = MSP_SUCCESS == ret;
            if (isLogin) {
                isrAudioStatus = MSP_AUDIO_SAMPLE_FIRST;
            } else {
                LOG_E("MSPLogin failure! ret: {},", ret);
                Utils::sendNativeMessage(
                    nativeMessageCreate(
                        NativeEvent_voiceHelperCommandParsed,
                        {.as_ptr = voiceCommandCreate(VoiceCommandState_disconnect, nullptr)},
                        0,
                        (CopyPtrValue)voiceCommandCopy,
                        (FreePtrValue)voiceCommandDestroy
                    )
                );
            }
        }
    } else {
        Utils::sendNativeMessage(
            nativeMessageCreate(
                NativeEvent_voiceHelperCommandParsed,
                {.as_ptr = voiceCommandCreate(VoiceCommandState_disconnect, nullptr)},
                0,
                (CopyPtrValue)voiceCommandCopy,
                (FreePtrValue)voiceCommandDestroy
            )
        );
    }
}

} // namespace MK