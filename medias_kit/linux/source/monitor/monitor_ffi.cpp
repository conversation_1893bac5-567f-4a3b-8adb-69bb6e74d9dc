#include "monitor/monitor_ffi.h"

#include "monitor/monitor.hpp"
#include "renderer/texture_renderer.hpp"
#include "stream&capture/video_capture.hpp"
#include "types/types_ffi.h"
#include "utils/logger.hpp"

MonitorRef
monitorCreate() {
    return new MK::Monitor();
}

int64_t
monitorInit(
    MonitorRef monitor,
    const char *pluginId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    int width,
    int height,
    int framerate
) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    return monitorPtr->init(pluginId, videoCaptureDevice, width, height, framerate);
}

void
monitorDestroy(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    delete monitorPtr;
}

void
monitorStart(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    monitorPtr->start();
}

void
monitorStop(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    monitorPtr->stop();
}
