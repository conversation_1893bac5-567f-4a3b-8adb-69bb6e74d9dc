#include "monitor/monitor.hpp"

#include <cassert>
#include <epoxy/gl.h>

#include "renderer/texture_renderer_ffi.h"
#include "utils/logger.hpp"

namespace MK {

Monitor::Monitor() {}

Monitor::~Monitor() {
    stop();
    if (capture) {
        delete capture;
    }
    if (renderer) {
        textureRendererDestroy(renderer);
    }
    LOG_I("{}", "Memory free!");
}

int64_t
Monitor::init(
    const char *pluginId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const int width,
    const int height,
    const int framerate
) {
    renderer = (TextureRenderer *)textureRendererCreate(pluginId);
    if (!renderer) {
        return 0;
    }

    capture = new VideoCapture(
        videoCaptureDevice,
        PixelFormat_rgba,
        width,
        height,
        framerate,
        [this, width, height](
            const PixelFormat pixelFormat,
            const uint8_t *const frame,
            const long frameLen
        ) {
            assert(pixelFormat == PixelFormat_rgba);
            if (!this->pause) {
                this->renderer->renderRgbaFrame(frame, frameLen, width, height);
            }
        }
    );

    if (!capture->init()) {
        return 0;
    }
    return renderer->getTexture();
}

void
Monitor::start() {
    pause = false;
}

void
Monitor::stop() {
    pause = true;
}

} // namespace MK
