#include "utils/utils.hpp"

#include <chrono>
#include <iomanip>
#include <map>
#include <memory>
#include <mutex>
#include <sstream>
#include <string>

#include "medias_kit_plugin_private.hpp"
#include "utils/logger.hpp"

namespace MK {

std::shared_ptr<std::string>
Utils::int2hex(const int value, const int minWidth) {
    std::stringstream ss;
    ss << "0x" << std::setw(minWidth) << std::setfill('0') << std::hex << value;
    return std::make_shared<std::string>(ss.str());
}

int
Utils::getCodeRunTime(
    const std::function<void()> func,
    const std::string &label,
    const bool enableLog
) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    auto duration =
        std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
            .count();
    if (enableLog) {
        LOG_I("{} Run time: {}ms", label, duration);
    }
    return duration;
}

void
Utils::sendNativeMessage(
    const NativeMessageRef message,
    const char *const pluginId
) {
    std::string pluginIdStr = pluginId ? pluginId : "";
    FindPluginCallback callback = [&](const std::string &pluginId, const MediasKitPlugin &plugin) {
        Dart_CObject obj;
        obj.type = Dart_CObject_Type::Dart_CObject_kNativePointer;
        obj.value.as_native_pointer.ptr = (intptr_t)nativeMessageCopy(message);
        obj.value.as_native_pointer.size = sizeof(NativeMessage);
        // 自己管理内存, callback 只有在消息没有触达 dart 层时才会调用
        obj.value.as_native_pointer.callback = [](void *isolate_callback_data, void *peer) {
            nativeMessageDestroy((NativeMessage *)peer);
        };
        auto success = Dart_PostCObject_DL(plugin.sendPort, &obj);
        if (!success) {
            LOG_E("{}", "Dart_PostCObject_DL failure!");
        }
    };
    if (pluginIdStr.empty()) {
        enumeratePlugins(callback);
    } else {
        findPlugin(pluginIdStr, callback);
    }
    nativeMessageDestroy(message);
}

} // namespace MK