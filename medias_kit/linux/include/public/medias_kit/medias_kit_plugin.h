#ifndef FLUTTER_PLUGIN_MEDIAS_KIT_PLUGIN_H_
#define FLUTTER_PLUGIN_MEDIAS_KIT_PLUGIN_H_

#include <flutter_linux/flutter_linux.h>

G_BEGIN_DECLS

#ifdef FLUTTER_PLUGIN_IMPL
#define FLUTTER_PLUGIN_EXPORT __attribute__((visibility("default")))
#else
#define FLUTTER_PLUGIN_EXPORT
#endif

/// 插件对象
typedef struct {
    /// 父类实例
    GObject parent_instance;

    /// 方法通道
    FlMethodChannel *channel;

    /// 纹理注册器
    FlTextureRegistrar *textureRegistrar;

    /// 视图
    FlView *view;

    /// 事件发送端口
    int64_t sendPort;
} MediasKitPlugin;

typedef struct {
    GObjectClass parent_class;
} MediasKitPluginClass;

FLUTTER_PLUGIN_EXPORT GType
medias_kit_plugin_get_type();

FLUTTER_PLUGIN_EXPORT void
medias_kit_plugin_register_with_registrar(FlPluginRegistrar *registrar);

G_END_DECLS

#endif // FLUTTER_PLUGIN_MEDIAS_KIT_PLUGIN_H_
