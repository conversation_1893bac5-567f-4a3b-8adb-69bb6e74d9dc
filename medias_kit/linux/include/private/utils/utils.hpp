#pragma once

#include <cstdint>
#include <dart_api_dl.h>
#include <functional>
#include <memory>
#include <string>

#include "types/types_ffi.h"

namespace MK {

/// 便利工具集
class Utils {
  public:
    static std::shared_ptr<std::string>
    int2hex(const int value, const int minWidth = 2);

    static int
    getCodeRunTime(
        const std::function<void()> func,
        const std::string &label = "",
        const bool enableLog = true
    );

    /**
     * @brief 发送原生消息
     *
     * @param message 消息, 请通过 nativeMessageCreate 创建, 发送后会自动释放
     * @param pluginId 插件ID, 如果不为空, 则只发送给指定插件, 否则发送给所有插件
     */
    static void
    sendNativeMessage(
        const NativeMessageRef message,
        const char *const pluginId = nullptr
    );
};

} // namespace MK