#pragma once

#include <atomic>

#include "device/device_ffi.h"
#include "renderer/texture_renderer.hpp"
#include "stream&capture/video_capture.hpp"

namespace MK {

class Monitor {
  private:
    std::atomic_bool pause = false;

    VideoCapture *capture;

    TextureRenderer *renderer;

  public:
    Monitor();

    ~Monitor();

    int64_t
    init(
        const char *pluginId,
        const VideoCaptureDeviceRef videoCaptureDevice,
        const int width,
        const int height,
        const int framerate
    );

    void
    start();

    void
    stop();
};
} // namespace MK