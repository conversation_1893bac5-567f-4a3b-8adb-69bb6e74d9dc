#ifndef MONITOR_FFI_H
#define MONITOR_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "device/device_ffi.h"
#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 监视器引用类型
typedef void *MonitorRef;

/// 创建监视器, 返回监视器引用
ATTRIBUTES MonitorRef
monitorCreate();

/// 初始化监视器
///
/// - [monitor] 监视器引用
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// - [device] 视频采集设备, [HostDevice.videoSources] 中获取
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// 返回 OpenGL 纹理 ID
ATTRIBUTES int64_t
monitorInit(
    MonitorRef monitor,
    const char *pluginId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    int width,
    int height,
    int framerate
);

/// 释放监视器
///
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorDestroy(MonitorRef monitor);

/// 开始监视
///
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorStart(MonitorRef monitor);

/// 停止监视
///
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorStop(MonitorRef monitor);

#ifdef __cplusplus
}
#endif

#endif // MONITOR_FFI_H
