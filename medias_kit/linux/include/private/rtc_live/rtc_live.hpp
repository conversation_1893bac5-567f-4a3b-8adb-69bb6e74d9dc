#pragma once

#include <functional>
#include <mutex>
#include <string>

#include "NGIAgoraLocalUser.h"
#include "NGIAgoraRtcConnection.h"
#include "renderer/audio_renderer.hpp"
#include "stream&capture/local_stream.hpp"
#include "utils/flutter_api.hpp"

namespace MK {

class RtcLive : public ILocalVideoStreamController,
                public ILocalAudioStreamController,
                public agora::rtc::IRtcConnectionObserver,
                public agora::rtc::INetworkObserver,
                public agora::rtc::ILocalUserObserver,
                public agora::media::IAudioFrameObserverBase {
  public:
    typedef std::function<void(char *, int)> CallbackAudio;

    AudioRenderer *const audioRenderer;

    std::shared_ptr<LocalAudioStream> localAudioStream;

    std::shared_ptr<LocalVideoStream> localVideoStream;

  private:
    agora::base::IAgoraService *service = nullptr;

    agora::agora_refptr<agora::rtc::IRtcConnection> connection;

    agora::agora_refptr<agora::rtc::IMediaNodeFactory> factory;

    bool pause = false;

    bool enableSpeak = false;

    bool isInit = false;

    bool connected = false;

  public:
    const std::shared_ptr<const std::string> streamType;

    const std::shared_ptr<const std::string> appId;

    const std::shared_ptr<const std::string> token;

    const std::shared_ptr<const std::string> channelId;

    const std::shared_ptr<const std::string> userId;

    std::mutex callbackMtx;
    CallbackAudio callbackAudio;

    RtcLive(
        const std::shared_ptr<const std::string> streamType,
        const std::shared_ptr<const std::string> appId,
        const std::shared_ptr<const std::string> token,
        const std::shared_ptr<const std::string> channelId,
        const std::shared_ptr<const std::string> userId,
        const VideoCaptureDeviceRef videoCaptureDevice,
        const std::shared_ptr<const std::string> audioCaptureName,
        const std::shared_ptr<const std::string> audioRenderName,
        const double audioRenderVolume,
        const int width,
        const int height,
        const int framerate,
        const int bitrate,
        const int sampleRate,
        const int numChannels
    );

    ~RtcLive() override;

    bool
    init();

    void
    attachRemoteAudioHandler(CallbackAudio callbackAudio);

    void
    start();

    void
    stop();

    void
    speak(bool enable);

    bool
    updateAudioCapture(const std::shared_ptr<const std::string> audioCaptureName);

    bool
    updateAudioRender(
        const std::shared_ptr<const std::string> audioRenderName,
        const double volume
    );

#pragma mark-- ILocalVideoStreamController

    agora::agora_refptr<agora::rtc::IVideoFrameSender>
    createVideoFrameSender() const override;

    agora::agora_refptr<agora::rtc::ILocalVideoTrack>
    createCustomVideoTrack(
        agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
    ) const override;

    int
    publishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const override;

    int
    unpublishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const override;

    bool
    canCommitVideo() const override;

    bool
    videoHandler(
        const PixelFormat pixelFormat,
        const void *frame,
        const size_t frameLen,
        const size_t width,
        const size_t height
    ) const override;

#pragma mark-- ILocalAudioStreamController

    agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
    createAudioPcmDataSender() const override;

    agora::agora_refptr<agora::rtc::ILocalAudioTrack>
    createCustomAudioTrack(
        agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
    ) const override;

    int
    publishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const override;

    int
    unpublishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const override;

    AudioCommitType
    canCommitAudio() const override;

    const std::shared_ptr<const std::vector<char>>
    audioHandler(const char *frame, size_t frameLen) override;

#pragma mark-- IRtcConnectionObserver

    void
    onConnected(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override;
    void
    onDisconnected(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override;
    void
    onConnecting(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override;
    void
    onReconnecting(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override;
    void
    onReconnected(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override;
    void
    onConnectionLost(const agora::rtc::TConnectionInfo &connectionInfo
    ) override;
    void
    onLastmileQuality(const agora::rtc::QUALITY_TYPE quality) override {}
    void
    onLastmileProbeResult(const agora::rtc::LastmileProbeResult &result
    ) override {}
    void
    onTokenPrivilegeWillExpire(const char *token) override;
    void
    onTokenPrivilegeDidExpire() override;
    void
    onConnectionFailure(
        const agora::rtc::TConnectionInfo &connectionInfo,
        agora::rtc::CONNECTION_CHANGED_REASON_TYPE reason
    ) override {}
    void
    onUserJoined(agora::user_id_t userId) override;
    void
    onUserLeft(
        agora::user_id_t userId, agora::rtc::USER_OFFLINE_REASON_TYPE reason
    ) override;
    void
    onTransportStats(const agora::rtc::RtcStats &stats) override;
    void
    onChannelMediaRelayStateChanged(int state, int code) override {}
    void
    onUserNetworkQuality(
        agora::user_id_t userId,
        agora::rtc::QUALITY_TYPE txQuality,
        agora::rtc::QUALITY_TYPE rxQuality
    ) override;
    void
    onNetworkTypeChanged(agora::rtc::NETWORK_TYPE type) override;

#pragma mark-- INetworkObserver

    void
    onUplinkNetworkInfoUpdated(const agora::rtc::UplinkNetworkInfo &info
    ) override {}

#pragma mark-- IAudioFrameObserverBase

    bool
    onPlaybackAudioFrameBeforeMixing(
        const char *channelId,
        agora::media::base::user_id_t userId,
        AudioFrame &audioFrame
    ) override;
    bool
    onPlaybackAudioFrame(const char *channelId, AudioFrame &audioFrame) override;
    bool
    onRecordAudioFrame(const char *channelId, AudioFrame &audioFrame) override {
        return true;
    };
    bool
    onMixedAudioFrame(const char *channelId, AudioFrame &audioFrame) override {
        return true;
    };
    bool
    onEarMonitoringAudioFrame(AudioFrame &audioFrame) override {
        return true;
    };
    AudioParams
    getEarMonitoringAudioParams() override {
        return AudioParams();
    };
    int
    getObservedAudioFramePosition() override {
        return 0;
    };
    AudioParams
    getPlaybackAudioParams() override {
        return AudioParams();
    };
    AudioParams
    getRecordAudioParams() override {
        return AudioParams();
    };
    AudioParams
    getMixedAudioParams() override {
        return AudioParams();
    };

#pragma mark-- ILocalUserObserver

    void
    onAudioTrackPublishStart(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override {}
    void
    onAudioTrackPublishSuccess(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override {}
    void
    onAudioTrackUnpublished(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override {}
    void
    onAudioTrackPublicationFailure(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack,
        agora::ERROR_CODE_TYPE error
    ) override {}
    void
    onLocalAudioTrackStatistics(const agora::rtc::LocalAudioStats &stats
    ) override {}
    void
    onRemoteAudioTrackStatistics(
        agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack,
        const agora::rtc::RemoteAudioTrackStats &stats
    ) override;
    void
    onUserAudioTrackSubscribed(
        agora::user_id_t userId,
        agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack
    ) override;
    void
    onUserAudioTrackStateChanged(
        agora::user_id_t userId,
        agora::agora_refptr<agora::rtc::IRemoteAudioTrack> audioTrack,
        agora::rtc::REMOTE_AUDIO_STATE state,
        agora::rtc::REMOTE_AUDIO_STATE_REASON reason,
        int elapsed
    ) override;
    void
    onVideoTrackPublishStart(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override {}
    void
    onVideoTrackPublishSuccess(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override {}
    void
    onVideoTrackPublicationFailure(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
        agora::ERROR_CODE_TYPE error
    ) override {}
    void
    onVideoTrackUnpublished(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override {}
    void
    onLocalVideoTrackStateChanged(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
        agora::rtc::LOCAL_VIDEO_STREAM_STATE state,
        agora::rtc::LOCAL_VIDEO_STREAM_REASON errorCode
    ) override {}
    void
    onLocalVideoTrackStatistics(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
        const agora::rtc::LocalVideoTrackStats &stats
    ) override {}
    void
    onUserVideoTrackSubscribed(
        agora::user_id_t userId,
        const agora::rtc::VideoTrackInfo &trackInfo,
        agora::agora_refptr<agora::rtc::IRemoteVideoTrack> videoTrack
    ) override {}
    void
    onUserVideoTrackStateChanged(
        agora::user_id_t userId,
        agora::agora_refptr<agora::rtc::IRemoteVideoTrack> videoTrack,
        agora::rtc::REMOTE_VIDEO_STATE state,
        agora::rtc::REMOTE_VIDEO_STATE_REASON reason,
        int elapsed
    ) override {}
    void
    onFirstRemoteVideoFrameRendered(
        agora::user_id_t userId,
        int width,
        int height,
        int elapsed
    ) override {}
    void
    onRemoteVideoTrackStatistics(
        agora::agora_refptr<agora::rtc::IRemoteVideoTrack> videoTrack,
        const agora::rtc::RemoteVideoTrackStats &stats
    ) override {}
    void
    onAudioVolumeIndication(
        const agora::rtc::AudioVolumeInformation *speakers,
        unsigned int speakerNumber,
        int totalVolume
    ) override {}
    void
    onActiveSpeaker(agora::user_id_t userId) override {}
    void
    onAudioSubscribeStateChanged(
        const char *channel,
        agora::user_id_t userId,
        agora::rtc::STREAM_SUBSCRIBE_STATE oldState,
        agora::rtc::STREAM_SUBSCRIBE_STATE newState,
        int elapseSinceLastState
    ) override {}
    void
    onVideoSubscribeStateChanged(
        const char *channel,
        agora::user_id_t userId,
        agora::rtc::STREAM_SUBSCRIBE_STATE oldState,
        agora::rtc::STREAM_SUBSCRIBE_STATE newState,
        int elapseSinceLastState
    ) override {}
    void
    onAudioPublishStateChanged(
        const char *channel,
        agora::rtc::STREAM_PUBLISH_STATE oldState,
        agora::rtc::STREAM_PUBLISH_STATE newState,
        int elapseSinceLastState
    ) override {}
    void
    onVideoPublishStateChanged(
        const char *channel,
        agora::rtc::STREAM_PUBLISH_STATE oldState,
        agora::rtc::STREAM_PUBLISH_STATE newState,
        int elapseSinceLastState
    ) override {}
    void
    onRemoteSubscribeFallbackToAudioOnly(
        agora::user_id_t userId, bool isFallbackOrRecover
    ) override {}
    void
    onFirstRemoteAudioFrame(agora::user_id_t userId, int elapsed) override {}
    void
    onFirstRemoteAudioDecoded(agora::user_id_t userId, int elapsed) override {}
    void
    onFirstRemoteVideoFrame(
        agora::user_id_t userId, int width, int height, int elapsed
    ) override {}
    void
    onFirstRemoteVideoDecoded(
        agora::user_id_t userId, int width, int height, int elapsed
    ) override {}
    void
    onVideoSizeChanged(
        agora::user_id_t userId, int width, int height, int rotation
    ) override {}
    void
    onUserInfoUpdated(agora::user_id_t userId, USER_MEDIA_INFO msg, bool val)
        override;
    void
    onIntraRequestReceived() override {}
    void
    onStreamMessage(
        agora::user_id_t userId, int streamId, const char *data, size_t length
    ) override {}
    void
    onUserStateChanged(agora::user_id_t userId, uint32_t state) override {}
};
} // namespace MK