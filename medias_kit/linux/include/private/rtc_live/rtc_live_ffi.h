#ifndef RTC_LIVE_FFI_H
#define RTC_LIVE_FFI_H

#include <stdint.h>

#include "device/device_ffi.h"
#include "recorder/recorder_ffi.h"
#include "rtmp_live/rtmp_live_ffi.h"
#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 用户离开原因
///
/// - [quit] 退出
/// - [offline] 掉线
/// - [roleChange] 角色改变
typedef enum : int32_t {
    /// 退出
    RtcLiveUserLeftReason_quit,

    /// 掉线
    RtcLiveUserLeftReason_offline,

    /// 角色改变
    RtcLiveUserLeftReason_roleChange,
} RtcLiveUserLeftReason;

/// RTC 状态
typedef struct {
    /// 频道用户数
    int userCount;

    /// 视频发送码率, 单位: kbps
    int sendVideoBitrate;

    /// 发送丢包率
    int sendLossRate;

    /// 系统 CPU 使用率 %
    int cpuTotalUsage;

    /// 系统内存使用率 %
    int memoryTotalUsage;

    /// 客户端到本地路由器的往返延时
    int gatewayRtt;
} RtcStats;

/// RTC 状态引用类型
typedef RtcStats *RtcStatsRef;

/// 创建 RTC 状态
///
/// - [userCount] 频道用户数
/// - [sendVideoBitrate] 视频发送码率, 单位: kbps
/// - [sendLossRate] 发送丢包率
/// - [cpuTotalUsage] 系统 CPU 使用率 %
/// - [memoryTotalUsage] 系统内存使用率 %
/// - [gatewayRtt] 客户端到本地路由器的往返延时
ATTRIBUTES RtcStatsRef
rtcStatsCreate(
    const int userCount,
    const int sendVideoBitrate,
    const int sendLossRate,
    const int cpuTotalUsage,
    const int memoryTotalUsage,
    const int gatewayRtt
);

/// 复制 RTC 状态
ATTRIBUTES RtcStatsRef
rtcStatsCopy(const RtcStatsRef stats);

/// 销毁 RTC 状态
ATTRIBUTES void
rtcStatsDestroy(const RtcStatsRef stats);

/// RTC 直播引用类型
typedef void *RtcLiveRef;

/// 创建 RTC 直播
///
/// - [streamType] 直播流类型 [StreamType]
/// - [appId] 应用ID
/// - [token] 鉴权token
/// - [channelId] 频道ID
/// - [userId] 用户ID
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [audioRenderName] 音频渲染设备名称
/// - [audioRenderVolume] 音频渲染音量
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [bitrate] 视频码率
/// - [sampleRate] 采样率
/// - [numChannels] 音频通道数
ATTRIBUTES RtcLiveRef
rtcLiveCreate(
    const char *const streamType,
    const char *const appId,
    const char *const token,
    const char *const channelId,
    const int userId,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const char *const audioRenderName,
    const int audioRenderVolume,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels
);

/// 销毁 RTC 直播
///
/// - [rtcLive] RTC 直播引用
ATTRIBUTES void
rtcLiveDestroy(RtcLiveRef rtcLive);

/// 初始化 RTC 直播
///
/// - [rtcLive] RTC 直播引用
ATTRIBUTES bool
rtcLiveInit(RtcLiveRef rtcLive);

/// 绑定远端音频处理器
///
/// - [rtcLive] RTC 直播引用
/// - [recorder] 录制器引用, 可空
/// - [rtmpLive] RTMP 直播引用, 可空
ATTRIBUTES void
attachRemoteAudioHandler(
    RtcLiveRef rtcLive,
    RecorderRef recorder,
    RtmpLiveRef rtmpLive
);

/// 开始 RTC 直播
///
/// - [rtcLive] RTC 直播引用
ATTRIBUTES void
rtcLiveStart(RtcLiveRef rtcLive);

/// 停止 RTC 直播
///
/// - [rtcLive] RTC 直播引用
ATTRIBUTES void
rtcLiveStop(RtcLiveRef rtcLive);

/// 设置是否允许说话
///
/// - [rtcLive] RTC 直播引用
/// - [enable] 是否允许说话
ATTRIBUTES void
rtcLiveSpeak(RtcLiveRef rtcLive, bool enable);

/// 切换音频输入源
///
/// - [rtcLive] RTC 直播引用
/// - [audioCaptureName] 音频输入源名称
ATTRIBUTES void
rtcLiveChangeAudioSource(RtcLiveRef rtcLive, const char *const audioCaptureName);

/// 切换音频输出源
///
/// - [rtcLive] RTC 直播引用
/// - [audioRenderName] 音频输出源名称
/// - [volume] 音量
ATTRIBUTES void
rtcLiveChangeAudioSink(
    RtcLiveRef rtcLive,
    const char *const audioRenderName,
    const double volume
);

#ifdef __cplusplus
}
#endif

#endif // RTC_LIVE_FFI_H
