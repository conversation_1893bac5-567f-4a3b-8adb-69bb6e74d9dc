#ifndef HELPER_FFI_H
#define HELPER_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 拷贝状态
///
/// - [copying] 拷贝中
/// - [success] 成功
/// - [failed] 失败
/// - [canceled] 已取消
typedef enum : int32_t {
    /// 拷贝中
    CopyFileState_copying,

    /// 成功
    CopyFileState_success,

    /// 失败
    CopyFileState_failed,

    /// 已取消
    CopyFileState_canceled,
} CopyFileState;

/// 拷贝信息
typedef struct {
    /// 目标文件路径
    char *dst;

    /// 拷贝状态
    CopyFileState state;

    /// 进度, 0.0 ~ 1.0
    double progress;

    /// 速度, 字节/秒
    int64_t speed;

    /// 剩余时间, 毫秒
    int64_t remainingTime;

    /// 错误信息
    char *error;
} CopyFileInfo;

/// 拷贝信息引用类型
typedef CopyFileInfo *CopyFileInfoRef;

/// 创建拷贝信息
///
/// - [dst] 目标文件路径
/// - [state] 拷贝状态
/// - [progress] 进度, 0.0 ~ 1.0
/// - [speed] 速度, 字节/秒
/// - [remainingTime] 剩余时间, 毫秒
/// - [error] 错误信息
ATTRIBUTES CopyFileInfoRef
copyFileInfoCreate(
    const char *const dst,
    const CopyFileState state,
    const double progress,
    const int64_t speed,
    const int64_t remainingTime,
    const char *const error
);

/// 拷贝拷贝信息
///
/// - [info] 拷贝信息
ATTRIBUTES CopyFileInfoRef
copyFileInfoCopy(const CopyFileInfoRef info);

/// 销毁拷贝信息
///
/// - [info] 拷贝信息引用
ATTRIBUTES void
copyFileInfoDestroy(const CopyFileInfoRef info);

/// 拷贝文件, 并回调进度
///
/// - [src] 源文件路径
/// - [dst] 目标文件路径
/// - [callbackPluginId] 回调插件ID, 为 nullptr 则回调所有插件
/// 返回是否成功, true 表示新建拷贝任务成功, false 表示任务已存在
ATTRIBUTES bool
copyFile(
    const char *const src,
    const char *const dst,
    const char *const callbackPluginId
);

/// 取消任务
///
/// - [dst] 目标文件路径
ATTRIBUTES void
cancelTask(const char *const dst);

/// 传输数据类型
typedef struct {
    /// 数据ID
    char *id;

    /// 数据
    uint8_t *value;

    /// 数据长度
    int64_t len;

    /// 行宽, 为0表示无行宽
    int64_t stride;

    /// 行高, 为0表示无高度
    int64_t height;
} TransferData;

/// 传输数据引用类型
typedef TransferData *TransferDataRef;

/// 创建传输数据
///
/// - [id] 数据ID
/// - [data] 数据源
/// - [len] 数据长度
/// - [stride] 行宽, 为0表示无行宽
/// - [height] 行高, 为0表示无高度
ATTRIBUTES TransferDataRef
transferDataCreate(
    const char *const id,
    const uint8_t *const data,
    const int64_t len,
    const int64_t stride,
    const int64_t height
);

/// 复制传输数据
///
/// - [data] 传输数据
ATTRIBUTES TransferDataRef
transferDataCopy(const TransferDataRef data);

/// 销毁传输数据
///
/// - [data] 传输数据
ATTRIBUTES void
transferDataDestroy(const TransferDataRef data);

/// 传输数据
///
/// - [pluginId] 接收数据的插件ID, 为 nullptr 则广播给所有插件
/// - [id] 数据ID
/// - [data] 数据
/// - [len] 数据长度
/// - [stride] 行宽, 为0表示无行宽
/// - [height] 行高, 为0表示无高度
ATTRIBUTES void
transferData(
    const char *const pluginId,
    const char *const id,
    const uint8_t *const data,
    const int64_t len,
    const int64_t stride,
    const int64_t height
);

#ifdef __cplusplus
}
#endif

#endif // HELPER_FFI_H
