#ifndef TEXTURE_RENDERER_FFI_H
#define TEXTURE_RENDERER_FFI_H

#include <stdint.h>

#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 纹理渲染器引用类型
typedef void *TextureRendererRef;

/// 创建纹理渲染器
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// 返回纹理渲染器引用
ATTRIBUTES TextureRendererRef
textureRendererCreate(const char *const pluginId);

/// 释放纹理渲染器
/// - [renderer] 纹理渲染器引用
ATTRIBUTES void
textureRendererDestroy(const TextureRendererRef renderer);

/// 渲染一帧数据
/// - [renderer] 纹理渲染器引用
/// - [frame] 帧数据
/// - [frameLen] 帧数据长度
ATTRIBUTES void
textureRendererRenderRgbaFrame(
    const TextureRendererRef renderer,
    const uint8_t *const frame,
    const long frameLen,
    const int64_t width,
    const int64_t height
);

/// 获取纹理ID
/// - [renderer] 纹理渲染器引用
/// 返回纹理ID，可用于Flutter的Texture组件
ATTRIBUTES int64_t
textureRendererGetTextureId(const TextureRendererRef renderer);

#ifdef __cplusplus
}
#endif

#endif // TEXTURE_RENDERER_FFI_H
