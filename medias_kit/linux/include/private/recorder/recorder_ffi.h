#ifndef RECORDER_FFI_H
#define RECORDER_FFI_H

#include <stdint.h>

#include "device/device_ffi.h"
#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 视频录制信息
typedef struct {
    /// 名称
    char *name;

    /// 存储路径, 为空代表失败
    char *path;

    /// 首个 I 帧, 偏移量
    int64_t pointOffset;

    /// 总帧数
    int64_t frameCount;
} VideoRecordInfo;

/// 视频录制信息引用类型
typedef VideoRecordInfo *VideoRecordInfoRef;

/// 创建视频录制信息
///
/// - [name] 视频名称
/// - [path] 视频路径
/// - [pointOffset] 首帧时间戳+错误偏移
/// - [frameCount] 帧总数
ATTRIBUTES VideoRecordInfoRef
videoRecordInfoCreate(
    const char *const name,
    const char *const path,
    const int64_t pointOffset,
    const int64_t frameCount
);

/// 复制视频录制信息
///
/// - [info] 视频录制信息引用
ATTRIBUTES VideoRecordInfoRef
videoRecordInfoCopy(const VideoRecordInfoRef info);

/// 销毁视频录制信息
///
/// - [info] 视频录制信息引用
ATTRIBUTES void
videoRecordInfoDestroy(const VideoRecordInfoRef info);

/// 录制器引用类型
typedef void *RecorderRef;

/// 创建录制器
///
/// - [spaceName] 录制空间名称
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [videoBitrate] 视频码率
/// - [sampleRate] 采样率
/// - [audioBitrate] 音频码率
/// - [numChannels] 音频通道数
ATTRIBUTES RecorderRef
recorderCreate(
    const char *const spaceName,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int videoBitrate,
    const int sampleRate,
    const int audioBitrate,
    const int numChannels
);

/// 销毁录制器
///
/// - [recorder] 录制器引用
ATTRIBUTES void
recorderDestroy(RecorderRef recorder);

/// 初始化录制器
///
/// - [recorder] 录制器引用
ATTRIBUTES bool
recorderInit(RecorderRef recorder);

/// 开始录制
///
/// - [recorder] 录制器引用
ATTRIBUTES void
recorderStart(RecorderRef recorder);

/// 停止录制
///
/// - [recorder] 录制器引用
ATTRIBUTES void
recorderStop(RecorderRef recorder);

/// 录制音频
///
/// - [recorder] 录制器引用
/// - [enable] true 录制音频, false 停止录制音频
ATTRIBUTES void
recorderRecordAudio(RecorderRef recorder, bool enable);

/// 录制远端音频
///
/// - [recorder] 录制器引用
/// - [frame] 音频帧数据
/// - [frameLen] 音频帧数据长度
ATTRIBUTES void
recorderRecordRemoteAudio(RecorderRef recorder, char *frame, int frameLen);

/// 合并视频
///
/// - [recorder] 录制器引用
/// - [name] 视频名称
/// - [beginTime] 开始时间
/// - [endTime] 结束时间
/// - [needEndPrecision] true 需要精确到毫秒, false 精确到秒
ATTRIBUTES void
recorderMerge(
    RecorderRef recorder,
    const char *const name,
    const long beginTime,
    const long endTime,
    const bool needEndPrecision
);

/// 获取帧总数
///
/// - [recorder] 录制器引用
ATTRIBUTES int64_t
recorderFrameCount(RecorderRef recorder);

/// 获取首帧时间戳+错误偏移
///
/// - [recorder] 录制器引用
ATTRIBUTES int64_t
recorderFirstFrameTimestampWithOffset(RecorderRef recorder);

/// 更新音频采集设备
///
/// - [recorder] 录制器引用
/// - [audioCaptureName] 音频采集设备名称
ATTRIBUTES bool
recorderUpdateAudioCapture(
    RecorderRef recorder,
    const char *const audioCaptureName
);

#ifdef __cplusplus
}
#endif

#endif // RECORDER_FFI_H
