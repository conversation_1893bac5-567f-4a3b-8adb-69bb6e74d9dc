#ifndef VECTOR_FFI_H
#define VECTOR_FFI_H

#include "macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 向量引用类型, 元素只能是指针
typedef void *VectorRef;

/// 释放元素的函数类型
typedef void (*FreeElement)(void *);

/// 创建向量
ATTRIBUTES VectorRef
vectorCreate();

/// 销毁向量, 并释放子元素
///
/// - [vector] 向量引用
/// - [freeElement] 释放元素的函数, 可空
ATTRIBUTES void
vectorDestroy(VectorRef vector, FreeElement freeElement);

/// 获取向量元素数量
///
/// - [vector] 向量引用
ATTRIBUTES int
vectorSize(VectorRef vector);

/// 获取向量元素
///
/// - [vector] 向量引用
/// - [index] 元素索引
ATTRIBUTES void *
vectorElementAt(VectorRef vector, int index);

#ifdef __cplusplus
}
#endif

#endif // VECTOR_FFI_H
