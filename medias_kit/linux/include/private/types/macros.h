#ifndef MACROS_H
#define MACROS_H

/**
 * @brief FFI 函数属性定义，用于跨语言调用
 *
 * @details 该宏定义了两个关键 GCC 属性：
 * - visibility("default"): 确保函数在动态链接库中可见，允许外部程序调用
 * - used: 防止编译器优化删除看似未使用的函数
 *
 * 这些属性确保函数使用 C 语言的命名约定（避免 C++ 名称修饰），
 * 并保证函数在共享库中可被外部程序访问。
 *
 * 注意：这些属性在 Linux/GCC 环境下有效。在 Windows 或其他编译器下，
 * 应使用相应的平台特定定义（如 Windows 下的 __declspec(dllexport)）。
 */
#define ATTRIBUTES __attribute__((visibility("default"))) __attribute__((used))

#endif // MACROS_H
