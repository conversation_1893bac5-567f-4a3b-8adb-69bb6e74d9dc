#ifndef TYPES_FFI_H
#define TYPES_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 像素格式枚举, 定义了视频帧的像素格式类型
///
/// - [nv12] YUV 4:2:0 格式，Y 平面后跟交错的 UV 平面
/// - [i420] YUV 4:2:0 格式，又称 YU12，三个独立平面 Y、U、V
/// - [rgba] RGBA 格式，每像素 4 字节，包含透明通道
typedef enum : int32_t {
    /// NV12 格式 (YUV 4:2:0，Y 平面后跟交错的 UV 平面)
    PixelFormat_nv12,

    /// I420 格式 (YUV 4:2:0，三个独立平面 Y、U、V)
    PixelFormat_i420,

    /// RGBA 格式 (每像素 4 字节，包含透明通道)
    PixelFormat_rgba,
} PixelFormat;

/// 核心事件枚举, 定义了核心模块的事件类型
///
/// - [bindNativePlugin] 绑定原生插件
/// - [bindDartApiDL] 绑定 Dart Api
typedef enum : int32_t {
    /// 绑定原生插件
    CoreEvent_bindNativePlugin,

    /// 绑定 Dart Api
    CoreEvent_bindDartApiDL,
} CoreEvent;

/// 原生事件枚举, 定义了原生模块的事件类型
///
/// - [videoCapturedDeviceChanged] 视频采集设备改变
/// - [audioSourceDeviceChanged] 音频输入设备改变
/// - [audioSinkDeviceChanged] 音频输出设备改变
/// - [videoMerged] 视频合并完成
/// - [videoSignalStatusChanged] 视频信号状态改变
/// - [test] 测试
typedef enum : int32_t {
    /// 视频采集设备改变, 参数为视频采集设备列表 VectorRef<VideoCaptureDeviceRef>
    NativeEvent_videoCapturedDeviceChanged,

    /// 音频输入设备改变, 参数为音频输入设备列表 VectorRef<AudioDeviceRef>
    NativeEvent_audioSourceDeviceChanged,

    /// 音频输出设备改变, 参数为音频输出设备列表 VectorRef<AudioDeviceRef>
    NativeEvent_audioSinkDeviceChanged,

    /// 视频合并完成, 参数为视频信息 [VideoRecordInfoRef]
    NativeEvent_videoMerged,

    /// 视频信号状态改变, 参数为有无视频信号 bool
    NativeEvent_videoSignalStatusChanged,

    /// RTC 直播用户进入, 参数为用户ID int
    NativeEvent_rtcLiveUserJoined,

    /// RTC 直播用户离开, 参数为(userId | reason << 32) int
    NativeEvent_rtcLiveUserLeft,

    /// RTC 直播传输状态, 参数为 RTC 状态 [RtcStatsRef]
    NativeEvent_rtcLiveTransportStats,

    /// RTC 直播网络质量, 参数为网络质量 int
    NativeEvent_rtcLiveNetworkQuality,

    /// RTC 直播网络类型, 参数为网络类型 int
    NativeEvent_rtcLiveNetworkType,

    /// RTC 直播重新初始化, 无参数
    NativeEvent_rtcLiveReInit,

    /// RTMP 直播视频数据处理, 参数为视频数据 [TransferDataRef]
    NativeEvent_rtmpLiveVideoHandler,

    /// 语音助手唤醒, 无参数
    NativeEvent_voiceHelperAwakened,

    /// 语音助手识别, 参数为识别结果字符串
    NativeEvent_voiceHelperRecognizing,

    /// 语音助手命令解析完成, 参数解析状态 [VoiceCommandRef]
    NativeEvent_voiceHelperCommandParsed,

    /// 文件拷贝, 参数为拷贝信息 [CopyFileInfoRef]
    NativeEvent_fileCopy,

    /// 数据传输, 参数为传输数据 [TransferDataRef]
    NativeEvent_dataTransfer,

    /// 测试
    NativeEvent_test,
} NativeEvent;

/// 流类型
///
/// - [main] 主流
/// - [secondary] 副流
typedef enum : int32_t {
    /// 主流
    StreamType_main,

    /// 副流
    StreamType_secondary,
} StreamType;

/// 释放原生事件指针参数的函数类型
typedef void (*FreePtrValue)(void *ptr);

/// 复制原生事件指针参数的函数类型
typedef void *(*CopyPtrValue)(void *ptr);

/// 原生事件参数
typedef union {
    /// 指针
    void *as_ptr;

    /// 布尔
    bool as_bool;

    /// 整数
    int64_t as_int;

    /// 浮点数
    double as_double;
} NativeValue;

/// 原生消息结构体, 用于传递原生事件和参数
typedef struct {
    /// 事件类型
    NativeEvent event;

    /// 事件参数
    NativeValue value;

    /// 扩展字段, 一般用于传递对象指针, 在接收方做映射校验
    int64_t extra;

    /// 复制原生事件指针参数, 对 dart 隐藏
    /// ignore: unused_field
    CopyPtrValue _copyPtr;

    /// 释放原生事件指针参数, 对 dart 隐藏
    /// ignore: unused_field
    FreePtrValue _freePtr;
} NativeMessage;

/// 原生消息引用类型
typedef NativeMessage *NativeMessageRef;

/// 创建原生消息
///
/// - [event] 事件类型
/// - [value] 事件参数
/// - [extra] 扩展字段, 一般用于传递对象指针, 在接收方做映射校验
/// - [copyPtr] 复制原生事件指针参数的函数, 如果 value 为指针类型, 则必须提供该函数
/// - [freePtr] 释放原生事件指针参数的函数, 如果 value 为指针类型, 则必须提供该函数
ATTRIBUTES NativeMessageRef
nativeMessageCreate(
    const NativeEvent event,
    const NativeValue value,
    const int64_t extra,
    const CopyPtrValue copyPtr,
    const FreePtrValue freePtr
);

/// 复制原生消息
///
/// - [message] 原生消息引用
ATTRIBUTES NativeMessageRef
nativeMessageCopy(const NativeMessageRef message);

/// 销毁原生消息, 并释放参数指针
///
/// - [message] 原生消息引用
ATTRIBUTES void
nativeMessageDestroy(const NativeMessageRef message);

/// 销毁原生消息，但不销毁参数指针，参数指针需要手动销毁
///
/// - [message] 原生消息引用
ATTRIBUTES void
nativeMessageDestroyWithoutValuePtr(const NativeMessageRef message);

#ifdef __cplusplus
}
#endif

#endif // TYPES_FFI_H
