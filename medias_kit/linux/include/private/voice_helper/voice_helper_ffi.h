#ifndef VOICE_HELPER_FFI_H
#define VOICE_HELPER_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 语音命令状态
///
/// - [success] 识别成功
/// - [disconnect] 连接丢失
/// - [unRecognize] 未识别
typedef enum : uint32_t {
    /// 识别成功
    VoiceCommandState_success,

    /// 未识别
    VoiceCommandState_unRecognize,

    /// 连接丢失
    VoiceCommandState_disconnect,
} VoiceCommandState;

/// 语音命令
typedef struct {
    /// 语音状态
    VoiceCommandState state;

    /// 语音内容
    char *content;
} VoiceCommand;

/// 语音命令引用类型
typedef VoiceCommand *VoiceCommandRef;

/// 创建语音命令
///
/// - [state] 语音状态
/// - [content] 语音内容
ATTRIBUTES VoiceCommandRef
voiceCommandCreate(
    const VoiceCommandState state,
    const char *const content
);

/// 复制语音命令
///
/// - [command] 语音命令引用
ATTRIBUTES VoiceCommandRef
voiceCommandCopy(const VoiceCommandRef command);

/// 销毁语音命令
///
/// - [command] 语音命令引用
ATTRIBUTES void
voiceCommandDestroy(const VoiceCommandRef command);

/// 语音助手准备
///
/// - [bundlePath] 资源路径
/// - [audioCaptureName] 音频采集设备名
/// - [audioRenderName] 音频播放设备名
/// - [renderVolume] 音量 0~1
ATTRIBUTES bool
voiceHelperPrepare(
    const char *const bundlePath,
    const char *const audioCaptureName,
    const char *const audioRenderName,
    const double volume
);

/// 语音助手禁用
ATTRIBUTES void
voiceHelperDisable();

/// 语音助手播报
///
/// - [text] 文本
ATTRIBUTES bool
voiceHelperSpeaking(const char *const text);

/// 语音助手等待命令
ATTRIBUTES void
voiceHelperWaitingCommand();

#ifdef __cplusplus
}
#endif

#endif // VOICE_HELPER_FFI_H
