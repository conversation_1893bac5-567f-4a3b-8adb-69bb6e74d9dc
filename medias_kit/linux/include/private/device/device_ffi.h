#ifndef DEVICE_FFI_H
#define DEVICE_FFI_H

#include <dart_api_dl.h>
#include <pulse/introspect.h>
#include <stdbool.h>

#include "types/macros.h"
#include "types/types_ffi.h"
#include "types/vector_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 音频设备
typedef struct {
    /// 是否为默认设备
    bool isDefault;

    /// 设备索引
    uint32_t index;

    /// 设备名称
    char *name;

    /// 设备描述
    char *description;

    /// 设备音量 0~100
    int volume;

    /// 设备通道数
    int channels;

    /// 设备静音
    int mute;
} AudioDevice;

/// 音频设备引用类型
typedef AudioDevice *AudioDeviceRef;

/// 创建音频设备
ATTRIBUTES AudioDeviceRef
audioDeviceCreate(
    const bool isDefault,
    const uint32_t index,
    const char *const name,
    const char *const description,
    const int volume,
    const int channels,
    const int mute
);

/// 复制音频设备
///
/// - [device] 音频设备引用
ATTRIBUTES AudioDeviceRef
audioDeviceCopy(const AudioDeviceRef device);

/// 销毁音频设备
ATTRIBUTES void
audioDeviceDestroy(const AudioDeviceRef device);

/// 视频采集设备
typedef struct {
    /// 设备索引
    int index;

    /// 设备路径
    char *path;

    /// 设备名称
    char *name;

    /// true 为 USB 扩展, false 为内置PCIE设备
    bool isUsbExtend;
} VideoCaptureDevice;

/// 视频采集设备引用类型
typedef VideoCaptureDevice *VideoCaptureDeviceRef;

/// 创建视频采集设备
///
/// - [index] 设备索引
/// - [path] 设备路径
/// - [name] 设备名称
/// - [isUsbExtend] true 为 USB 扩展, false 为内置PCIE设备
ATTRIBUTES VideoCaptureDeviceRef
videoCaptureDeviceCreate(
    const int index,
    const char *const path,
    const char *const name,
    const bool isUsbExtend
);

/// 复制视频采集设备
///
/// - [device] 视频采集设备引用
ATTRIBUTES VideoCaptureDeviceRef
videoCaptureDeviceCopy(const VideoCaptureDeviceRef device);

/// 销毁视频采集设备
///
/// - [device] 视频采集设备引用
ATTRIBUTES void
videoCaptureDeviceDestroy(const VideoCaptureDeviceRef device);

/// 主机设备磁盘空间信息
typedef struct {
    /// 总容量
    uint64_t capacity;

    /// 总剩余空间
    uint64_t free;

    /// 当前用户配额下剩余空间
    uint64_t available;
} HostDeviceSpaceInfo;

/// 主机设备磁盘空间信息引用类型
typedef HostDeviceSpaceInfo *HostDeviceSpaceInfoRef;

/// 创建主机设备磁盘空间信息
///
/// - [capacity] 总容量
/// - [free] 总剩余空间
/// - [available] 当前用户配额下剩余空间
ATTRIBUTES HostDeviceSpaceInfoRef
hostDeviceSpaceInfoCreate(
    const uint64_t capacity,
    const uint64_t free,
    const uint64_t available
);

/// 复制主机设备磁盘空间信息
///
/// - [info] 主机设备磁盘空间信息引用
ATTRIBUTES HostDeviceSpaceInfoRef
hostDeviceSpaceInfoCopy(const HostDeviceSpaceInfoRef info);

/// 销毁主机设备磁盘空间信息
///
/// - [info] 主机设备磁盘空间信息引用
ATTRIBUTES void
hostDeviceSpaceInfoDestroy(const HostDeviceSpaceInfoRef info);

/// 获取主机运行环境平台信息
/// 返回值为静态变量不需要 [malloc.free]
ATTRIBUTES const char *
getHostDevicePlatform();

/// 获取主机指定磁盘空间信息
///
/// - [path] 磁盘路径
/// 返回值需要 [malloc.free]
ATTRIBUTES HostDeviceSpaceInfoRef
getHostDeviceSpaceInfo(const char *const path);

/// 开始监听视频采集设备
ATTRIBUTES void
startListenVideoCaptureDevices();

/// 开始监听音频设备
ATTRIBUTES void
startListenAudioDevices();

/// 设置音频源音量
///
/// - [device] 音频设备
/// - [volume] 音量 0~100
ATTRIBUTES void
setAudioSourceVolume(const AudioDeviceRef device, const int volume);

/// 设置音频输出音量
///
/// - [device] 音频设备
/// - [volume] 音量 0~100
ATTRIBUTES void
setAudioSinkVolume(const AudioDeviceRef device, const int volume);

/// 设置默认音频输出设备
///
/// - [device] 音频设备
ATTRIBUTES void
setDefaultAudioSink(const AudioDeviceRef device);

/// 设置默认音频输入设备
///
/// - [device] 音频设备
ATTRIBUTES void
setDefaultAudioSource(const AudioDeviceRef device);

/// 设置设备是否网络在线, 需根据网络状态及时更新
///
/// - [isOnline] 是否网络在线
ATTRIBUTES void
setDeviceIsOnline(const bool isOnline);

/// 获取设备是否网络在线, 仅供 native 使用
ATTRIBUTES bool
getDeviceIsOnline();

#ifdef __cplusplus
}
#endif

#endif // DEVICE_FFI_H
