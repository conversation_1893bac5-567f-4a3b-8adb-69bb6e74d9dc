#pragma once

#include <queue>

#include "NGIAgoraRtmpConnection.h"
#include "NGIAgoraRtmpLocalUser.h"
#include "stream&capture/audio_capture.hpp"
#include "stream&capture/local_stream.hpp"
#include "stream&capture/video_capture.hpp"

namespace MK {

class RtmpLive : public ILocalVideoStreamController,
                 public ILocalAudioStreamController,
                 public agora::rtc::IRtmpConnectionObserver,
                 public agora::rtc::IRtmpLocalUserObserver {

  public:
    std::shared_ptr<LocalAudioStream> localAudioStream;

    std::shared_ptr<LocalVideoStream> localVideoStream;

  private:
    agora::base::IAgoraService *service = nullptr;

    agora::agora_refptr<agora::rtc::IRtmpConnection> connection;

    agora::agora_refptr<agora::rtc::IMediaNodeFactory> factory;

    bool pause = false;

    bool enableSpeak = false;

    bool isInit = false;

    bool connected = false;

    std::mutex remoteMtx;

    std::condition_variable mainCV;

    std::queue<std::shared_ptr<const std::vector<char>>> rtcAudioFrames;

  public:
    const std::shared_ptr<const std::string> streamType;

    const std::shared_ptr<const std::string> appId;

    const std::shared_ptr<const std::string> url;

    const std::shared_ptr<const std::string> videoHandlerPluginId;

    const bool needVideoHandle;

    RtmpLive(
        const std::shared_ptr<const std::string> streamType,
        const std::shared_ptr<const std::string> appId,
        const std::shared_ptr<const std::string> url,
        const VideoCaptureDeviceRef videoCaptureDevice,
        const std::shared_ptr<const std::string> audioCaptureName,
        const int width,
        const int height,
        const int framerate,
        const int bitrate,
        const int sampleRate,
        const int numChannels,
        const std::shared_ptr<const std::string> videoHandlerPluginId,
        const bool needVideoHandle
    );

    ~RtmpLive() override;

    bool
    init();

    void
    start();

    void
    stop();

    void
    speak(bool enable);

    bool
    updateAudioCapture(const std::shared_ptr<const std::string> audioCaptureName);

    void
    pushRtcRemoteAudio(const char *const frame, const int frameLen);

    void
    pushCustomRgbaVideo(
        const uint8_t *const frame,
        const size_t frameLen,
        const size_t width,
        const size_t height
    );

#pragma mark-- ILocalVideoStreamController

    agora::agora_refptr<agora::rtc::IVideoFrameSender>
    createVideoFrameSender() const override;

    agora::agora_refptr<agora::rtc::ILocalVideoTrack>
    createCustomVideoTrack(
        agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
    ) const override;

    int
    publishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const override;

    int
    unpublishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const override;

    bool
    canCommitVideo() const override;

    bool
    videoHandler(
        const PixelFormat pixelFormat,
        const void *frame,
        const size_t frameLen,
        const size_t width,
        const size_t height
    ) const override;

#pragma mark-- ILocalAudioStreamController

    agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
    createAudioPcmDataSender() const override;

    agora::agora_refptr<agora::rtc::ILocalAudioTrack>
    createCustomAudioTrack(
        agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
    ) const override;

    int
    publishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const override;

    int
    unpublishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const override;

    AudioCommitType
    canCommitAudio() const override;

    const std::shared_ptr<const std::vector<char>>
    audioHandler(const char *frame, size_t frameLen) override;

#pragma mark-- IRtmpConnectionObserver

    void
    onReconnecting(const agora::rtc::RtmpConnectionInfo &connectionInfo
    ) override;
    void
    onConnected(const agora::rtc::RtmpConnectionInfo &connectionInfo) override;
    void
    onReconnected(const agora::rtc::RtmpConnectionInfo &connectionInfo
    ) override;
    void
    onDisconnected(const agora::rtc::RtmpConnectionInfo &connectionInfo
    ) override;
    void
    onConnectionFailure(
        const agora::rtc::RtmpConnectionInfo &connectionInfo,
        agora::rtc::RTMP_CONNECTION_ERROR errCode
    ) override;
    void
    onTransferStatistics(
        uint64_t video_width,
        uint64_t video_height,
        uint64_t video_bitrate,
        uint64_t audio_bitrate,
        uint64_t video_frame_rate,
        uint64_t push_video_frame_cnt,
        uint64_t pop_video_frame_cnt
    ) override;

#pragma mark-- IRtmpLocalUserObserver

    void
    onAudioTrackPublishStart(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override {}
    void
    onAudioTrackPublishSuccess(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override;
    void
    onAudioTrackPublicationFailure(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack,
        agora::rtc::PublishAudioError error
    ) override;
    void
    onAudioTrackUnpublished(
        agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) override {}
    void
    onVideoTrackPublishStart(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override {}
    void
    onVideoTrackPublishSuccess(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override;
    void
    onVideoTrackPublicationFailure(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
        agora::rtc::PublishVideoError error
    ) override;
    void
    onVideoTrackUnpublished(
        agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) override {}
};
} // namespace MK