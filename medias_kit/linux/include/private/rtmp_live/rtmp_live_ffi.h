#ifndef RTMP_LIVE_FFI_H
#define RTMP_LIVE_FFI_H

#include <stdint.h>

#include "device/device_ffi.h"
#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// RTMP 直播引用类型
typedef void *RtmpLiveRef;

/// 创建 RTMP 直播
///
/// - [streamType] 直播流类型 [StreamType]
/// - [appId] 应用ID
/// - [url] 推流地址
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [bitrate] 视频码率
/// - [sampleRate] 采样率
/// - [numChannels] 音频通道数
/// - [videoHandlerPluginId] 视频处理器插件ID
/// - [needVideoHandle] 是否需要视频处理
ATTRIBUTES RtmpLiveRef
rtmpLiveCreate(
    const char *const streamType,
    const char *const appId,
    const char *const url,
    const VideoCaptureDeviceRef videoCaptureDevice,
    const char *const audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels,
    const char *const videoHandlerPluginId,
    const bool needVideoHandle
);

/// 销毁 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
ATTRIBUTES void
rtmpLiveDestroy(RtmpLiveRef rtmpLive);

/// 初始化 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
ATTRIBUTES bool
rtmpLiveInit(RtmpLiveRef rtmpLive);

/// 开始 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
ATTRIBUTES void
rtmpLiveStart(RtmpLiveRef rtmpLive);

/// 停止 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
ATTRIBUTES void
rtmpLiveStop(RtmpLiveRef rtmpLive);

/// 设置是否允许说话
///
/// - [rtmpLive] RTMP 直播引用
/// - [enable] 是否允许说话
ATTRIBUTES void
rtmpLiveSpeak(RtmpLiveRef rtmpLive, bool enable);

/// 切换音频输入源
///
/// - [rtmpLive] RTMP 直播引用
/// - [audioCaptureName] 音频输入源名称
ATTRIBUTES void
rtmpLiveChangeAudioSource(
    RtmpLiveRef rtmpLive,
    const char *const audioCaptureName
);

/// 推送 RTC 音频
///
/// - [rtmpLive] RTMP 直播引用
/// - [frame] 音频帧数据
/// - [frameLen] 音频帧数据长度
ATTRIBUTES void
rtmpLivePushRtcRemoteAudio(
    RtmpLiveRef rtmpLive,
    const char *const frame,
    const int frameLen
);

/// 推送自定义 RGBA 视频
///
/// - [rtmpLive] RTMP 直播引用
/// - [frame] 视频帧数据
/// - [frameLen] 视频帧数据长度
/// - [width] 视频宽度
/// - [height] 视频高度
ATTRIBUTES void
rtmpLivePushCustomRgbaVideo(
    RtmpLiveRef rtmpLive,
    const uint8_t *const frame,
    const int frameLen,
    const int width,
    const int height
);

#ifdef __cplusplus
}
#endif

#endif // RTMP_LIVE_FFI_H
