#pragma once

#include <memory>
#include <string>

#include "IAgoraService.h"
#include "NGIAgoraAudioTrack.h"
#include "stream&capture/audio_capture.hpp"
#include "stream&capture/video_capture.hpp"

namespace MK {

class ILocalVideoStreamController {
  public:
    virtual ~ILocalVideoStreamController() = default;

    virtual agora::agora_refptr<agora::rtc::IVideoFrameSender>
    createVideoFrameSender() const = 0;

    virtual agora::agora_refptr<agora::rtc::ILocalVideoTrack>
    createCustomVideoTrack(
        agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
    ) const = 0;

    virtual int
    publishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const = 0;

    virtual int
    unpublishVideo(agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
    ) const = 0;

    virtual bool
    canCommitVideo() const = 0;

    virtual bool
    videoHandler(
        const PixelFormat pixelFormat,
        const void *frame,
        const size_t frameLen,
        const size_t width,
        const size_t height
    ) const = 0;
};

enum class AudioCommitType {
    disable,
    local,
    remote,
    mix,
};

class ILocalAudioStreamController {

  public:
    virtual ~ILocalAudioStreamController() = default;

    virtual agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
    createAudioPcmDataSender() const = 0;

    virtual agora::agora_refptr<agora::rtc::ILocalAudioTrack>
    createCustomAudioTrack(
        agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
    ) const = 0;

    virtual int
    publishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const = 0;

    virtual int
    unpublishAudio(agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
    ) const = 0;

    virtual AudioCommitType
    canCommitAudio() const = 0;

    virtual const std::shared_ptr<const std::vector<char>>
    audioHandler(const char *frame, size_t frameLen) = 0;
};

class LocalVideoStream {

    const ILocalVideoStreamController *const controller;

    agora::agora_refptr<agora::rtc::IVideoFrameSender> sender;

    agora::agora_refptr<agora::rtc::ILocalVideoTrack> track;

  public:
    VideoCapture *const capture;

    const int bitrate;

    LocalVideoStream(
        const ILocalVideoStreamController *const controller,
        const VideoCaptureDeviceRef videoCaptureDevice,
        const int width,
        const int height,
        const int framerate,
        const int bitrate
    );

    ~LocalVideoStream();

    bool
    init();

    bool
    commitVideo(
        const PixelFormat pixelFormat,
        const void *frame,
        const size_t frameLen,
        const size_t width = 0,
        const size_t height = 0
    );
};

class LocalAudioStream {

    ILocalAudioStreamController *controller;

    agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender;

    agora::agora_refptr<agora::rtc::ILocalAudioTrack> track;

  public:
    AudioCapture *const capture;

    LocalAudioStream(
        ILocalAudioStreamController *controller,
        const std::shared_ptr<const std::string> audioCaptureName,
        const int sampleRate,
        const int numChannels
    );

    ~LocalAudioStream();

    bool
    init();

    bool
    commitAudio(const void *frame, const size_t frameLen);
};

} // namespace MK