#pragma once

#include <AgoraMediaBase.h>
#include <functional>
#include <map>
#include <memory>
#include <mutex>

#include "device/device_ffi.h"
#include "types/types_ffi.h"

namespace MK {

class InternalCapture;

class VideoCapture {
  public:
    typedef std::function<void(const PixelFormat, const uint8_t *const, const long)> FrameCallBack;

    friend class InternalCapture;

  private:
    const VideoCaptureDeviceRef videoCaptureDevice;

    bool _hasSignal = false;

    void *channel = nullptr;

    void *handle = nullptr;

    bool isInit = false;

    std::mutex mtx;

    static void
    onCaptureCallback(uint8_t *frame, long frameLen, void *param);

    FrameCallBack frameCallBack;

  public:
    const PixelFormat pixelFormat;

    const int width;

    const int height;

    const int framerate;

    VideoCapture(
        const VideoCaptureDeviceRef videoCaptureDevice,
        const PixelFormat pixelFormat,
        const int width,
        const int height,
        const int framerate,
        const FrameCallBack frameCallBack
    );

    ~VideoCapture();

    bool
    init();

    const VideoCaptureDevice &
    getVideoCaptureDevice() const;

    bool
    hasSignal() const;
};
} // namespace MK