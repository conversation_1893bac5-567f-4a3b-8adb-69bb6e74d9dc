#pragma once

#include <functional>
#include <map>

#include "medias_kit/medias_kit_plugin.h"
#include "types/types_ffi.h"
#include "utils/argument.hpp"

// This file exposes some plugin internals for unit testing. See
// https://github.com/flutter/flutter/issues/88724 for current limitations
// in the unit-testable API.

namespace MK {
/**
 * @brief 查找插件回调函数类型
 *
 * @param pluginId 插件ID
 * @param plugin 插件对象
 */
typedef std::function<void(const std::string &pluginId, const MediasKitPlugin &)> FindPluginCallback;

/**
 * @brief 获取插件对象
 *
 * @param pluginId 插件ID
 * @param callback 回调函数, 不要跨线程使用回调参数
 */
void
findPlugin(
    const std::string &pluginId,
    const FindPluginCallback callback
);

/**
 * @brief 枚举所有插件对象
 *
 * @param callback 回调函数, 不要跨线程使用回调参数
 */
void
enumeratePlugins(const FindPluginCallback callback);

/**
 * @brief 公共模块消息处理
 *
 * @param plugin 插件句柄
 * @param event 消息事件
 * @param argument 消息参数
 * @return FlMethodResponse* 数据回执
 */
FlMethodResponse *
coreMessageHandler(
    const MediasKitPlugin &plugin,
    const CoreEvent event,
    const std::shared_ptr<const Argument> argument
);

} // namespace MK
