import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';

// 使用 ffigen 生成的 addresses 中的符号
// final _dylib = DynamicLibrary.open('libmedias_kit_plugin.so');

/// 原生函数查找扩展
// extension LookupNativeFunctionExtension on Object {
//   /// 转换为原生函数指针
//   Pointer<NativeFunction<Void Function(Pointer<Void>)>> get toNative {
//     assert(toString().startsWith("Closure"));
//     return _dylib.lookup<NativeFunction<Void Function(Pointer<Void>)>>(
//       toString().split("'")[1],
//     );
//   }
// }

/// 字符指针扩展
extension PointerCharExtension on Pointer<Char> {
  /// 转换为 Dart 字符串
  String get dartString {
    try {
      return cast<Utf8>().toDartString();
    } catch (e) {
      debugPrint(e.toString());
    }
    return "";
  }
}

/// UTF8 字符指针扩展
extension PointerUtf8Extension on String {
  /// UTF8 字符指针释放器
  static final _finalizer = Finalizer((Pointer<Utf8> ptr) {
    malloc.free(ptr);
  });

  /// 转换为字符指针并添加释放器, 返回值生命周期等于字符串本身
  /// 注: 不可 attach 到临时变量上
  Pointer<Char> toNativeCharPointer(Object owner) {
    final ptr = toNativeUtf8();
    _finalizer.attach(owner, ptr);
    return ptr.cast();
  }
}
