// 功能: MediasKit FFI函数绑定

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/core/medias_kit_ffi.dart')
library;

import 'dart:ffi' as ffi;
import '' as self;

/// 创建原生消息
///
/// - [event] 事件类型
/// - [value] 事件参数
/// - [extra] 扩展字段, 一般用于传递对象指针, 在接收方做映射校验
/// - [copyPtr] 复制原生事件指针参数的函数, 如果 value 为指针类型, 则必须提供该函数
/// - [freePtr] 释放原生事件指针参数的函数, 如果 value 为指针类型, 则必须提供该函数
@ffi.Native<
  NativeMessageRef Function(
    ffi.Int32,
    NativeValue,
    ffi.Int64,
    CopyPtrValue,
    FreePtrValue,
  )
>(symbol: 'nativeMessageCreate')
external NativeMessageRef _nativeMessageCreate(
  int event,
  NativeValue value,
  int extra,
  CopyPtrValue copyPtr,
  FreePtrValue freePtr,
);

NativeMessageRef nativeMessageCreate(
  NativeEvent event,
  NativeValue value,
  int extra,
  CopyPtrValue copyPtr,
  FreePtrValue freePtr,
) => _nativeMessageCreate(event.value, value, extra, copyPtr, freePtr);

/// 复制原生消息
///
/// - [message] 原生消息引用
@ffi.Native<NativeMessageRef Function(NativeMessageRef)>()
external NativeMessageRef nativeMessageCopy(NativeMessageRef message);

/// 销毁原生消息, 并释放参数指针
///
/// - [message] 原生消息引用
@ffi.Native<ffi.Void Function(NativeMessageRef)>()
external void nativeMessageDestroy(NativeMessageRef message);

/// 销毁原生消息，但不销毁参数指针，参数指针需要手动销毁
///
/// - [message] 原生消息引用
@ffi.Native<ffi.Void Function(NativeMessageRef)>()
external void nativeMessageDestroyWithoutValuePtr(NativeMessageRef message);

/// 创建向量
@ffi.Native<VectorRef Function()>()
external VectorRef vectorCreate();

/// 销毁向量, 并释放子元素
///
/// - [vector] 向量引用
/// - [freeElement] 释放元素的函数, 可空
@ffi.Native<ffi.Void Function(VectorRef, FreeElement)>()
external void vectorDestroy(VectorRef vector, FreeElement freeElement);

/// 获取向量元素数量
///
/// - [vector] 向量引用
@ffi.Native<ffi.Int Function(VectorRef)>()
external int vectorSize(VectorRef vector);

/// 获取向量元素
///
/// - [vector] 向量引用
/// - [index] 元素索引
@ffi.Native<ffi.Pointer<ffi.Void> Function(VectorRef, ffi.Int)>()
external ffi.Pointer<ffi.Void> vectorElementAt(VectorRef vector, int index);

/// 创建音频设备
@ffi.Native<
  AudioDeviceRef Function(
    ffi.Bool,
    ffi.Uint32,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Int,
    ffi.Int,
    ffi.Int,
  )
>()
external AudioDeviceRef audioDeviceCreate(
  bool isDefault,
  int index,
  ffi.Pointer<ffi.Char> name,
  ffi.Pointer<ffi.Char> description,
  int volume,
  int channels,
  int mute,
);

/// 复制音频设备
///
/// - [device] 音频设备引用
@ffi.Native<AudioDeviceRef Function(AudioDeviceRef)>()
external AudioDeviceRef audioDeviceCopy(AudioDeviceRef device);

/// 销毁音频设备
@ffi.Native<ffi.Void Function(AudioDeviceRef)>()
external void audioDeviceDestroy(AudioDeviceRef device);

/// 创建视频采集设备
///
/// - [index] 设备索引
/// - [path] 设备路径
/// - [name] 设备名称
/// - [isUsbExtend] true 为 USB 扩展, false 为内置PCIE设备
@ffi.Native<
  VideoCaptureDeviceRef Function(
    ffi.Int,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Bool,
  )
>()
external VideoCaptureDeviceRef videoCaptureDeviceCreate(
  int index,
  ffi.Pointer<ffi.Char> path,
  ffi.Pointer<ffi.Char> name,
  bool isUsbExtend,
);

/// 复制视频采集设备
///
/// - [device] 视频采集设备引用
@ffi.Native<VideoCaptureDeviceRef Function(VideoCaptureDeviceRef)>()
external VideoCaptureDeviceRef videoCaptureDeviceCopy(
  VideoCaptureDeviceRef device,
);

/// 销毁视频采集设备
///
/// - [device] 视频采集设备引用
@ffi.Native<ffi.Void Function(VideoCaptureDeviceRef)>()
external void videoCaptureDeviceDestroy(VideoCaptureDeviceRef device);

/// 创建主机设备磁盘空间信息
///
/// - [capacity] 总容量
/// - [free] 总剩余空间
/// - [available] 当前用户配额下剩余空间
@ffi.Native<
  HostDeviceSpaceInfoRef Function(ffi.Uint64, ffi.Uint64, ffi.Uint64)
>()
external HostDeviceSpaceInfoRef hostDeviceSpaceInfoCreate(
  int capacity,
  int free,
  int available,
);

/// 复制主机设备磁盘空间信息
///
/// - [info] 主机设备磁盘空间信息引用
@ffi.Native<HostDeviceSpaceInfoRef Function(HostDeviceSpaceInfoRef)>()
external HostDeviceSpaceInfoRef hostDeviceSpaceInfoCopy(
  HostDeviceSpaceInfoRef info,
);

/// 销毁主机设备磁盘空间信息
///
/// - [info] 主机设备磁盘空间信息引用
@ffi.Native<ffi.Void Function(HostDeviceSpaceInfoRef)>()
external void hostDeviceSpaceInfoDestroy(HostDeviceSpaceInfoRef info);

/// 获取主机运行环境平台信息
/// 返回值为静态变量不需要 [malloc.free]
@ffi.Native<ffi.Pointer<ffi.Char> Function()>()
external ffi.Pointer<ffi.Char> getHostDevicePlatform();

/// 获取主机指定磁盘空间信息
///
/// - [path] 磁盘路径
/// 返回值需要 [malloc.free]
@ffi.Native<HostDeviceSpaceInfoRef Function(ffi.Pointer<ffi.Char>)>()
external HostDeviceSpaceInfoRef getHostDeviceSpaceInfo(
  ffi.Pointer<ffi.Char> path,
);

/// 开始监听视频采集设备
@ffi.Native<ffi.Void Function()>()
external void startListenVideoCaptureDevices();

/// 开始监听音频设备
@ffi.Native<ffi.Void Function()>()
external void startListenAudioDevices();

/// 设置音频源音量
///
/// - [device] 音频设备
/// - [volume] 音量 0~100
@ffi.Native<ffi.Void Function(AudioDeviceRef, ffi.Int)>(isLeaf: true)
external void setAudioSourceVolume(AudioDeviceRef device, int volume);

/// 设置音频输出音量
///
/// - [device] 音频设备
/// - [volume] 音量 0~100
@ffi.Native<ffi.Void Function(AudioDeviceRef, ffi.Int)>(isLeaf: true)
external void setAudioSinkVolume(AudioDeviceRef device, int volume);

/// 设置默认音频输出设备
///
/// - [device] 音频设备
@ffi.Native<ffi.Void Function(AudioDeviceRef)>(isLeaf: true)
external void setDefaultAudioSink(AudioDeviceRef device);

/// 设置默认音频输入设备
///
/// - [device] 音频设备
@ffi.Native<ffi.Void Function(AudioDeviceRef)>(isLeaf: true)
external void setDefaultAudioSource(AudioDeviceRef device);

/// 设置设备是否网络在线, 需根据网络状态及时更新
///
/// - [isOnline] 是否网络在线
@ffi.Native<ffi.Void Function(ffi.Bool)>()
external void setDeviceIsOnline(bool isOnline);

/// 创建纹理渲染器
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// 返回纹理渲染器引用
@ffi.Native<TextureRendererRef Function(ffi.Pointer<ffi.Char>)>()
external TextureRendererRef textureRendererCreate(
  ffi.Pointer<ffi.Char> pluginId,
);

/// 释放纹理渲染器
/// - [renderer] 纹理渲染器引用
@ffi.Native<ffi.Void Function(TextureRendererRef)>()
external void textureRendererDestroy(TextureRendererRef renderer);

/// 渲染一帧数据
/// - [renderer] 纹理渲染器引用
/// - [frame] 帧数据
/// - [frameLen] 帧数据长度
@ffi.Native<
  ffi.Void Function(
    TextureRendererRef,
    ffi.Pointer<ffi.Uint8>,
    ffi.Long,
    ffi.Int64,
    ffi.Int64,
  )
>()
external void textureRendererRenderRgbaFrame(
  TextureRendererRef renderer,
  ffi.Pointer<ffi.Uint8> frame,
  int frameLen,
  int width,
  int height,
);

/// 获取纹理ID
/// - [renderer] 纹理渲染器引用
/// 返回纹理ID，可用于Flutter的Texture组件
@ffi.Native<ffi.Int64 Function(TextureRendererRef)>()
external int textureRendererGetTextureId(TextureRendererRef renderer);

/// 创建监视器, 返回监视器引用
@ffi.Native<MonitorRef Function()>()
external MonitorRef monitorCreate();

/// 初始化监视器
///
/// - [monitor] 监视器引用
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// - [device] 视频采集设备, [HostDevice.videoSources] 中获取
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// 返回 OpenGL 纹理 ID
@ffi.Native<
  ffi.Int64 Function(
    MonitorRef,
    ffi.Pointer<ffi.Char>,
    VideoCaptureDeviceRef,
    ffi.Int,
    ffi.Int,
    ffi.Int,
  )
>(isLeaf: true)
external int monitorInit(
  MonitorRef monitor,
  ffi.Pointer<ffi.Char> pluginId,
  VideoCaptureDeviceRef videoCaptureDevice,
  int width,
  int height,
  int framerate,
);

/// 释放监视器
///
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorDestroy(MonitorRef monitor);

/// 开始监视
///
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorStart(MonitorRef monitor);

/// 停止监视
///
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorStop(MonitorRef monitor);

/// 创建视频录制信息
///
/// - [name] 视频名称
/// - [path] 视频路径
/// - [pointOffset] 首帧时间戳+错误偏移
/// - [frameCount] 帧总数
@ffi.Native<
  VideoRecordInfoRef Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Int64,
    ffi.Int64,
  )
>()
external VideoRecordInfoRef videoRecordInfoCreate(
  ffi.Pointer<ffi.Char> name,
  ffi.Pointer<ffi.Char> path,
  int pointOffset,
  int frameCount,
);

/// 复制视频录制信息
///
/// - [info] 视频录制信息引用
@ffi.Native<VideoRecordInfoRef Function(VideoRecordInfoRef)>()
external VideoRecordInfoRef videoRecordInfoCopy(VideoRecordInfoRef info);

/// 销毁视频录制信息
///
/// - [info] 视频录制信息引用
@ffi.Native<ffi.Void Function(VideoRecordInfoRef)>()
external void videoRecordInfoDestroy(VideoRecordInfoRef info);

/// 创建录制器
///
/// - [spaceName] 录制空间名称
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [videoBitrate] 视频码率
/// - [sampleRate] 采样率
/// - [audioBitrate] 音频码率
/// - [numChannels] 音频通道数
@ffi.Native<
  RecorderRef Function(
    ffi.Pointer<ffi.Char>,
    VideoCaptureDeviceRef,
    ffi.Pointer<ffi.Char>,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
  )
>(isLeaf: true)
external RecorderRef recorderCreate(
  ffi.Pointer<ffi.Char> spaceName,
  VideoCaptureDeviceRef videoCaptureDevice,
  ffi.Pointer<ffi.Char> audioCaptureName,
  int width,
  int height,
  int framerate,
  int videoBitrate,
  int sampleRate,
  int audioBitrate,
  int numChannels,
);

/// 销毁录制器
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Void Function(RecorderRef)>()
external void recorderDestroy(RecorderRef recorder);

/// 初始化录制器
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Bool Function(RecorderRef)>()
external bool recorderInit(RecorderRef recorder);

/// 开始录制
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Void Function(RecorderRef)>()
external void recorderStart(RecorderRef recorder);

/// 停止录制
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Void Function(RecorderRef)>()
external void recorderStop(RecorderRef recorder);

/// 录制音频
///
/// - [recorder] 录制器引用
/// - [enable] true 录制音频, false 停止录制音频
@ffi.Native<ffi.Void Function(RecorderRef, ffi.Bool)>()
external void recorderRecordAudio(RecorderRef recorder, bool enable);

/// 录制远端音频
///
/// - [recorder] 录制器引用
/// - [frame] 音频帧数据
/// - [frameLen] 音频帧数据长度
@ffi.Native<ffi.Void Function(RecorderRef, ffi.Pointer<ffi.Char>, ffi.Int)>()
external void recorderRecordRemoteAudio(
  RecorderRef recorder,
  ffi.Pointer<ffi.Char> frame,
  int frameLen,
);

/// 合并视频
///
/// - [recorder] 录制器引用
/// - [name] 视频名称
/// - [beginTime] 开始时间
/// - [endTime] 结束时间
/// - [needEndPrecision] true 需要精确到毫秒, false 精确到秒
@ffi.Native<
  ffi.Void Function(
    RecorderRef,
    ffi.Pointer<ffi.Char>,
    ffi.Long,
    ffi.Long,
    ffi.Bool,
  )
>()
external void recorderMerge(
  RecorderRef recorder,
  ffi.Pointer<ffi.Char> name,
  int beginTime,
  int endTime,
  bool needEndPrecision,
);

/// 获取帧总数
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Int64 Function(RecorderRef)>()
external int recorderFrameCount(RecorderRef recorder);

/// 获取首帧时间戳+错误偏移
///
/// - [recorder] 录制器引用
@ffi.Native<ffi.Int64 Function(RecorderRef)>()
external int recorderFirstFrameTimestampWithOffset(RecorderRef recorder);

/// 更新音频采集设备
///
/// - [recorder] 录制器引用
/// - [audioCaptureName] 音频采集设备名称
@ffi.Native<ffi.Bool Function(RecorderRef, ffi.Pointer<ffi.Char>)>()
external bool recorderUpdateAudioCapture(
  RecorderRef recorder,
  ffi.Pointer<ffi.Char> audioCaptureName,
);

/// 创建 RTC 状态
///
/// - [userCount] 频道用户数
/// - [sendVideoBitrate] 视频发送码率, 单位: kbps
/// - [sendLossRate] 发送丢包率
/// - [cpuTotalUsage] 系统 CPU 使用率 %
/// - [memoryTotalUsage] 系统内存使用率 %
/// - [gatewayRtt] 客户端到本地路由器的往返延时
@ffi.Native<
  RtcStatsRef Function(ffi.Int, ffi.Int, ffi.Int, ffi.Int, ffi.Int, ffi.Int)
>()
external RtcStatsRef rtcStatsCreate(
  int userCount,
  int sendVideoBitrate,
  int sendLossRate,
  int cpuTotalUsage,
  int memoryTotalUsage,
  int gatewayRtt,
);

/// 复制 RTC 状态
@ffi.Native<RtcStatsRef Function(RtcStatsRef)>()
external RtcStatsRef rtcStatsCopy(RtcStatsRef stats);

/// 销毁 RTC 状态
@ffi.Native<ffi.Void Function(RtcStatsRef)>()
external void rtcStatsDestroy(RtcStatsRef stats);

/// 创建 RTC 直播
///
/// - [streamType] 直播流类型 [StreamType]
/// - [appId] 应用ID
/// - [token] 鉴权token
/// - [channelId] 频道ID
/// - [userId] 用户ID
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [audioRenderName] 音频渲染设备名称
/// - [audioRenderVolume] 音频渲染音量
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [bitrate] 视频码率
/// - [sampleRate] 采样率
/// - [numChannels] 音频通道数
@ffi.Native<
  RtcLiveRef Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Int,
    VideoCaptureDeviceRef,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
  )
>(isLeaf: true)
external RtcLiveRef rtcLiveCreate(
  ffi.Pointer<ffi.Char> streamType,
  ffi.Pointer<ffi.Char> appId,
  ffi.Pointer<ffi.Char> token,
  ffi.Pointer<ffi.Char> channelId,
  int userId,
  VideoCaptureDeviceRef videoCaptureDevice,
  ffi.Pointer<ffi.Char> audioCaptureName,
  ffi.Pointer<ffi.Char> audioRenderName,
  int audioRenderVolume,
  int width,
  int height,
  int framerate,
  int bitrate,
  int sampleRate,
  int numChannels,
);

/// 销毁 RTC 直播
///
/// - [rtcLive] RTC 直播引用
@ffi.Native<ffi.Void Function(RtcLiveRef)>()
external void rtcLiveDestroy(RtcLiveRef rtcLive);

/// 初始化 RTC 直播
///
/// - [rtcLive] RTC 直播引用
@ffi.Native<ffi.Bool Function(RtcLiveRef)>()
external bool rtcLiveInit(RtcLiveRef rtcLive);

/// 绑定远端音频处理器
///
/// - [rtcLive] RTC 直播引用
/// - [recorder] 录制器引用, 可空
/// - [rtmpLive] RTMP 直播引用, 可空
@ffi.Native<ffi.Void Function(RtcLiveRef, RecorderRef, RtmpLiveRef)>()
external void attachRemoteAudioHandler(
  RtcLiveRef rtcLive,
  RecorderRef recorder,
  RtmpLiveRef rtmpLive,
);

/// 开始 RTC 直播
///
/// - [rtcLive] RTC 直播引用
@ffi.Native<ffi.Void Function(RtcLiveRef)>()
external void rtcLiveStart(RtcLiveRef rtcLive);

/// 停止 RTC 直播
///
/// - [rtcLive] RTC 直播引用
@ffi.Native<ffi.Void Function(RtcLiveRef)>()
external void rtcLiveStop(RtcLiveRef rtcLive);

/// 设置是否允许说话
///
/// - [rtcLive] RTC 直播引用
/// - [enable] 是否允许说话
@ffi.Native<ffi.Void Function(RtcLiveRef, ffi.Bool)>()
external void rtcLiveSpeak(RtcLiveRef rtcLive, bool enable);

/// 切换音频输入源
///
/// - [rtcLive] RTC 直播引用
/// - [audioCaptureName] 音频输入源名称
@ffi.Native<ffi.Void Function(RtcLiveRef, ffi.Pointer<ffi.Char>)>()
external void rtcLiveChangeAudioSource(
  RtcLiveRef rtcLive,
  ffi.Pointer<ffi.Char> audioCaptureName,
);

/// 切换音频输出源
///
/// - [rtcLive] RTC 直播引用
/// - [audioRenderName] 音频输出源名称
/// - [volume] 音量
@ffi.Native<ffi.Void Function(RtcLiveRef, ffi.Pointer<ffi.Char>, ffi.Double)>()
external void rtcLiveChangeAudioSink(
  RtcLiveRef rtcLive,
  ffi.Pointer<ffi.Char> audioRenderName,
  double volume,
);

/// 创建 RTMP 直播
///
/// - [streamType] 直播流类型 [StreamType]
/// - [appId] 应用ID
/// - [url] 推流地址
/// - [videoCaptureDevice] 视频采集设备
/// - [audioCaptureName] 音频采集设备名称
/// - [width] 采集宽度
/// - [height] 采集高度
/// - [framerate] 采集帧率
/// - [bitrate] 视频码率
/// - [sampleRate] 采样率
/// - [numChannels] 音频通道数
/// - [videoHandlerPluginId] 视频处理器插件ID
/// - [needVideoHandle] 是否需要视频处理
@ffi.Native<
  RtmpLiveRef Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    VideoCaptureDeviceRef,
    ffi.Pointer<ffi.Char>,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Int,
    ffi.Pointer<ffi.Char>,
    ffi.Bool,
  )
>(isLeaf: true)
external RtmpLiveRef rtmpLiveCreate(
  ffi.Pointer<ffi.Char> streamType,
  ffi.Pointer<ffi.Char> appId,
  ffi.Pointer<ffi.Char> url,
  VideoCaptureDeviceRef videoCaptureDevice,
  ffi.Pointer<ffi.Char> audioCaptureName,
  int width,
  int height,
  int framerate,
  int bitrate,
  int sampleRate,
  int numChannels,
  ffi.Pointer<ffi.Char> videoHandlerPluginId,
  bool needVideoHandle,
);

/// 销毁 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
@ffi.Native<ffi.Void Function(RtmpLiveRef)>()
external void rtmpLiveDestroy(RtmpLiveRef rtmpLive);

/// 初始化 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
@ffi.Native<ffi.Bool Function(RtmpLiveRef)>()
external bool rtmpLiveInit(RtmpLiveRef rtmpLive);

/// 开始 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
@ffi.Native<ffi.Void Function(RtmpLiveRef)>()
external void rtmpLiveStart(RtmpLiveRef rtmpLive);

/// 停止 RTMP 直播
///
/// - [rtmpLive] RTMP 直播引用
@ffi.Native<ffi.Void Function(RtmpLiveRef)>()
external void rtmpLiveStop(RtmpLiveRef rtmpLive);

/// 设置是否允许说话
///
/// - [rtmpLive] RTMP 直播引用
/// - [enable] 是否允许说话
@ffi.Native<ffi.Void Function(RtmpLiveRef, ffi.Bool)>()
external void rtmpLiveSpeak(RtmpLiveRef rtmpLive, bool enable);

/// 切换音频输入源
///
/// - [rtmpLive] RTMP 直播引用
/// - [audioCaptureName] 音频输入源名称
@ffi.Native<ffi.Void Function(RtmpLiveRef, ffi.Pointer<ffi.Char>)>()
external void rtmpLiveChangeAudioSource(
  RtmpLiveRef rtmpLive,
  ffi.Pointer<ffi.Char> audioCaptureName,
);

/// 推送 RTC 音频
///
/// - [rtmpLive] RTMP 直播引用
/// - [frame] 音频帧数据
/// - [frameLen] 音频帧数据长度
@ffi.Native<ffi.Void Function(RtmpLiveRef, ffi.Pointer<ffi.Char>, ffi.Int)>()
external void rtmpLivePushRtcRemoteAudio(
  RtmpLiveRef rtmpLive,
  ffi.Pointer<ffi.Char> frame,
  int frameLen,
);

/// 推送自定义 RGBA 视频
///
/// - [rtmpLive] RTMP 直播引用
/// - [frame] 视频帧数据
/// - [frameLen] 视频帧数据长度
/// - [width] 视频宽度
/// - [height] 视频高度
@ffi.Native<
  ffi.Void Function(
    RtmpLiveRef,
    ffi.Pointer<ffi.Uint8>,
    ffi.Int,
    ffi.Int,
    ffi.Int,
  )
>()
external void rtmpLivePushCustomRgbaVideo(
  RtmpLiveRef rtmpLive,
  ffi.Pointer<ffi.Uint8> frame,
  int frameLen,
  int width,
  int height,
);

/// 创建语音命令
///
/// - [state] 语音状态
/// - [content] 语音内容
@ffi.Native<VoiceCommandRef Function(ffi.Uint32, ffi.Pointer<ffi.Char>)>(
  symbol: 'voiceCommandCreate',
)
external VoiceCommandRef _voiceCommandCreate(
  int state,
  ffi.Pointer<ffi.Char> content,
);

VoiceCommandRef voiceCommandCreate(
  VoiceCommandState state,
  ffi.Pointer<ffi.Char> content,
) => _voiceCommandCreate(state.value, content);

/// 复制语音命令
///
/// - [command] 语音命令引用
@ffi.Native<VoiceCommandRef Function(VoiceCommandRef)>()
external VoiceCommandRef voiceCommandCopy(VoiceCommandRef command);

/// 销毁语音命令
///
/// - [command] 语音命令引用
@ffi.Native<ffi.Void Function(VoiceCommandRef)>()
external void voiceCommandDestroy(VoiceCommandRef command);

/// 语音助手准备
///
/// - [bundlePath] 资源路径
/// - [audioCaptureName] 音频采集设备名
/// - [audioRenderName] 音频播放设备名
/// - [renderVolume] 音量 0~1
@ffi.Native<
  ffi.Bool Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Double,
  )
>()
external bool voiceHelperPrepare(
  ffi.Pointer<ffi.Char> bundlePath,
  ffi.Pointer<ffi.Char> audioCaptureName,
  ffi.Pointer<ffi.Char> audioRenderName,
  double volume,
);

/// 语音助手禁用
@ffi.Native<ffi.Void Function()>()
external void voiceHelperDisable();

/// 语音助手播报
///
/// - [text] 文本
@ffi.Native<ffi.Bool Function(ffi.Pointer<ffi.Char>)>()
external bool voiceHelperSpeaking(ffi.Pointer<ffi.Char> text);

/// 语音助手等待命令
@ffi.Native<ffi.Void Function()>()
external void voiceHelperWaitingCommand();

/// 创建拷贝信息
///
/// - [dst] 目标文件路径
/// - [state] 拷贝状态
/// - [progress] 进度, 0.0 ~ 1.0
/// - [speed] 速度, 字节/秒
/// - [remainingTime] 剩余时间, 毫秒
/// - [error] 错误信息
@ffi.Native<
  CopyFileInfoRef Function(
    ffi.Pointer<ffi.Char>,
    ffi.Int32,
    ffi.Double,
    ffi.Int64,
    ffi.Int64,
    ffi.Pointer<ffi.Char>,
  )
>(symbol: 'copyFileInfoCreate')
external CopyFileInfoRef _copyFileInfoCreate(
  ffi.Pointer<ffi.Char> dst,
  int state,
  double progress,
  int speed,
  int remainingTime,
  ffi.Pointer<ffi.Char> error,
);

CopyFileInfoRef copyFileInfoCreate(
  ffi.Pointer<ffi.Char> dst,
  CopyFileState state,
  double progress,
  int speed,
  int remainingTime,
  ffi.Pointer<ffi.Char> error,
) => _copyFileInfoCreate(
  dst,
  state.value,
  progress,
  speed,
  remainingTime,
  error,
);

/// 拷贝拷贝信息
///
/// - [info] 拷贝信息
@ffi.Native<CopyFileInfoRef Function(CopyFileInfoRef)>()
external CopyFileInfoRef copyFileInfoCopy(CopyFileInfoRef info);

/// 销毁拷贝信息
///
/// - [info] 拷贝信息引用
@ffi.Native<ffi.Void Function(CopyFileInfoRef)>()
external void copyFileInfoDestroy(CopyFileInfoRef info);

/// 拷贝文件, 并回调进度
///
/// - [src] 源文件路径
/// - [dst] 目标文件路径
/// - [callbackPluginId] 回调插件ID, 为 nullptr 则回调所有插件
/// 返回是否成功, true 表示新建拷贝任务成功, false 表示任务已存在
@ffi.Native<
  ffi.Bool Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
  )
>()
external bool copyFile(
  ffi.Pointer<ffi.Char> src,
  ffi.Pointer<ffi.Char> dst,
  ffi.Pointer<ffi.Char> callbackPluginId,
);

/// 取消任务
///
/// - [dst] 目标文件路径
@ffi.Native<ffi.Void Function(ffi.Pointer<ffi.Char>)>()
external void cancelTask(ffi.Pointer<ffi.Char> dst);

/// 创建传输数据
///
/// - [id] 数据ID
/// - [data] 数据源
/// - [len] 数据长度
/// - [stride] 行宽, 为0表示无行宽
/// - [height] 行高, 为0表示无高度
@ffi.Native<
  TransferDataRef Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Uint8>,
    ffi.Int64,
    ffi.Int64,
    ffi.Int64,
  )
>()
external TransferDataRef transferDataCreate(
  ffi.Pointer<ffi.Char> id,
  ffi.Pointer<ffi.Uint8> data,
  int len,
  int stride,
  int height,
);

/// 复制传输数据
///
/// - [data] 传输数据
@ffi.Native<TransferDataRef Function(TransferDataRef)>()
external TransferDataRef transferDataCopy(TransferDataRef data);

/// 销毁传输数据
///
/// - [data] 传输数据
@ffi.Native<ffi.Void Function(TransferDataRef)>()
external void transferDataDestroy(TransferDataRef data);

/// 传输数据
///
/// - [pluginId] 接收数据的插件ID, 为 nullptr 则广播给所有插件
/// - [id] 数据ID
/// - [data] 数据
/// - [len] 数据长度
/// - [stride] 行宽, 为0表示无行宽
/// - [height] 行高, 为0表示无高度
@ffi.Native<
  ffi.Void Function(
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Char>,
    ffi.Pointer<ffi.Uint8>,
    ffi.Int64,
    ffi.Int64,
    ffi.Int64,
  )
>()
external void transferData(
  ffi.Pointer<ffi.Char> pluginId,
  ffi.Pointer<ffi.Char> id,
  ffi.Pointer<ffi.Uint8> data,
  int len,
  int stride,
  int height,
);

const addresses = _SymbolAddresses();

class _SymbolAddresses {
  const _SymbolAddresses();
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(NativeMessageRef)>>
  get nativeMessageDestroyWithoutValuePtr =>
      ffi.Native.addressOf(self.nativeMessageDestroyWithoutValuePtr);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(AudioDeviceRef)>>
  get audioDeviceDestroy => ffi.Native.addressOf(self.audioDeviceDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(VideoCaptureDeviceRef)>>
  get videoCaptureDeviceDestroy =>
      ffi.Native.addressOf(self.videoCaptureDeviceDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(HostDeviceSpaceInfoRef)>>
  get hostDeviceSpaceInfoDestroy =>
      ffi.Native.addressOf(self.hostDeviceSpaceInfoDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(VideoRecordInfoRef)>>
  get videoRecordInfoDestroy =>
      ffi.Native.addressOf(self.videoRecordInfoDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(RtcStatsRef)>>
  get rtcStatsDestroy => ffi.Native.addressOf(self.rtcStatsDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(VoiceCommandRef)>>
  get voiceCommandDestroy => ffi.Native.addressOf(self.voiceCommandDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(CopyFileInfoRef)>>
  get copyFileInfoDestroy => ffi.Native.addressOf(self.copyFileInfoDestroy);
  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(TransferDataRef)>>
  get transferDataDestroy => ffi.Native.addressOf(self.transferDataDestroy);
}

/// 像素格式枚举, 定义了视频帧的像素格式类型
///
/// - [nv12] YUV 4:2:0 格式，Y 平面后跟交错的 UV 平面
/// - [i420] YUV 4:2:0 格式，又称 YU12，三个独立平面 Y、U、V
/// - [rgba] RGBA 格式，每像素 4 字节，包含透明通道
enum PixelFormat {
  /// NV12 格式 (YUV 4:2:0，Y 平面后跟交错的 UV 平面)
  nv12(0),

  /// I420 格式 (YUV 4:2:0，三个独立平面 Y、U、V)
  i420(1),

  /// RGBA 格式 (每像素 4 字节，包含透明通道)
  rgba(2);

  final int value;
  const PixelFormat(this.value);

  static PixelFormat fromValue(int value) => switch (value) {
    0 => nv12,
    1 => i420,
    2 => rgba,
    _ => throw ArgumentError('Unknown value for PixelFormat: $value'),
  };
}

/// 核心事件枚举, 定义了核心模块的事件类型
///
/// - [bindNativePlugin] 绑定原生插件
/// - [bindDartApiDL] 绑定 Dart Api
enum CoreEvent {
  /// 绑定原生插件
  bindNativePlugin(0),

  /// 绑定 Dart Api
  bindDartApiDL(1);

  final int value;
  const CoreEvent(this.value);

  static CoreEvent fromValue(int value) => switch (value) {
    0 => bindNativePlugin,
    1 => bindDartApiDL,
    _ => throw ArgumentError('Unknown value for CoreEvent: $value'),
  };
}

/// 原生事件枚举, 定义了原生模块的事件类型
///
/// - [videoCapturedDeviceChanged] 视频采集设备改变
/// - [audioSourceDeviceChanged] 音频输入设备改变
/// - [audioSinkDeviceChanged] 音频输出设备改变
/// - [videoMerged] 视频合并完成
/// - [videoSignalStatusChanged] 视频信号状态改变
/// - [test] 测试
enum NativeEvent {
  /// 视频采集设备改变, 参数为视频采集设备列表 VectorRef<VideoCaptureDeviceRef>
  videoCapturedDeviceChanged(0),

  /// 音频输入设备改变, 参数为音频输入设备列表 VectorRef<AudioDeviceRef>
  audioSourceDeviceChanged(1),

  /// 音频输出设备改变, 参数为音频输出设备列表 VectorRef<AudioDeviceRef>
  audioSinkDeviceChanged(2),

  /// 视频合并完成, 参数为视频信息 [VideoRecordInfoRef]
  videoMerged(3),

  /// 视频信号状态改变, 参数为有无视频信号 bool
  videoSignalStatusChanged(4),

  /// RTC 直播用户进入, 参数为用户ID int
  rtcLiveUserJoined(5),

  /// RTC 直播用户离开, 参数为(userId | reason << 32) int
  rtcLiveUserLeft(6),

  /// RTC 直播传输状态, 参数为 RTC 状态 [RtcStatsRef]
  rtcLiveTransportStats(7),

  /// RTC 直播网络质量, 参数为网络质量 int
  rtcLiveNetworkQuality(8),

  /// RTC 直播网络类型, 参数为网络类型 int
  rtcLiveNetworkType(9),

  /// RTC 直播重新初始化, 无参数
  rtcLiveReInit(10),

  /// RTMP 直播视频数据处理, 参数为视频数据 [TransferDataRef]
  rtmpLiveVideoHandler(11),

  /// 语音助手唤醒, 无参数
  voiceHelperAwakened(12),

  /// 语音助手识别, 参数为识别结果字符串
  voiceHelperRecognizing(13),

  /// 语音助手命令解析完成, 参数解析状态 [VoiceCommandRef]
  voiceHelperCommandParsed(14),

  /// 文件拷贝, 参数为拷贝信息 [CopyFileInfoRef]
  fileCopy(15),

  /// 数据传输, 参数为传输数据 [TransferDataRef]
  dataTransfer(16),

  /// 测试
  test(17);

  final int value;
  const NativeEvent(this.value);

  static NativeEvent fromValue(int value) => switch (value) {
    0 => videoCapturedDeviceChanged,
    1 => audioSourceDeviceChanged,
    2 => audioSinkDeviceChanged,
    3 => videoMerged,
    4 => videoSignalStatusChanged,
    5 => rtcLiveUserJoined,
    6 => rtcLiveUserLeft,
    7 => rtcLiveTransportStats,
    8 => rtcLiveNetworkQuality,
    9 => rtcLiveNetworkType,
    10 => rtcLiveReInit,
    11 => rtmpLiveVideoHandler,
    12 => voiceHelperAwakened,
    13 => voiceHelperRecognizing,
    14 => voiceHelperCommandParsed,
    15 => fileCopy,
    16 => dataTransfer,
    17 => test,
    _ => throw ArgumentError('Unknown value for NativeEvent: $value'),
  };
}

/// 流类型
///
/// - [main] 主流
/// - [secondary] 副流
enum StreamType {
  /// 主流
  main(0),

  /// 副流
  secondary(1);

  final int value;
  const StreamType(this.value);

  static StreamType fromValue(int value) => switch (value) {
    0 => main,
    1 => secondary,
    _ => throw ArgumentError('Unknown value for StreamType: $value'),
  };
}

typedef FreePtrValueFunction = ffi.Void Function(ffi.Pointer<ffi.Void> ptr);
typedef DartFreePtrValueFunction = void Function(ffi.Pointer<ffi.Void> ptr);

/// 释放原生事件指针参数的函数类型
typedef FreePtrValue = ffi.Pointer<ffi.NativeFunction<FreePtrValueFunction>>;
typedef CopyPtrValueFunction =
    ffi.Pointer<ffi.Void> Function(ffi.Pointer<ffi.Void> ptr);

/// 复制原生事件指针参数的函数类型
typedef CopyPtrValue = ffi.Pointer<ffi.NativeFunction<CopyPtrValueFunction>>;

/// 原生事件参数
final class NativeValue extends ffi.Union {
  /// 指针
  external ffi.Pointer<ffi.Void> as_ptr;

  /// 布尔
  @ffi.Bool()
  external bool as_bool;

  /// 整数
  @ffi.Int64()
  external int as_int;

  /// 浮点数
  @ffi.Double()
  external double as_double;
}

/// 原生消息结构体, 用于传递原生事件和参数
final class NativeMessage extends ffi.Struct {
  /// 事件类型
  @ffi.Int32()
  external int eventAsInt;

  NativeEvent get event => NativeEvent.fromValue(eventAsInt);

  /// 事件参数
  external NativeValue value;

  /// 扩展字段, 一般用于传递对象指针, 在接收方做映射校验
  @ffi.Int64()
  external int extra;

  /// 复制原生事件指针参数, 对 dart 隐藏
  /// ignore: unused_field
  external CopyPtrValue _copyPtr;

  /// 释放原生事件指针参数, 对 dart 隐藏
  /// ignore: unused_field
  external FreePtrValue _freePtr;
}

/// 原生消息引用类型
typedef NativeMessageRef = ffi.Pointer<NativeMessage>;

/// 向量引用类型, 元素只能是指针
typedef VectorRef = ffi.Pointer<ffi.Void>;
typedef FreeElementFunction = ffi.Void Function(ffi.Pointer<ffi.Void>);
typedef DartFreeElementFunction = void Function(ffi.Pointer<ffi.Void>);

/// 释放元素的函数类型
typedef FreeElement = ffi.Pointer<ffi.NativeFunction<FreeElementFunction>>;

/// 音频设备
final class AudioDevice extends ffi.Struct {
  /// 是否为默认设备
  @ffi.Bool()
  external bool isDefault;

  /// 设备索引
  @ffi.Uint32()
  external int index;

  /// 设备名称
  external ffi.Pointer<ffi.Char> name;

  /// 设备描述
  external ffi.Pointer<ffi.Char> description;

  /// 设备音量 0~100
  @ffi.Int()
  external int volume;

  /// 设备通道数
  @ffi.Int()
  external int channels;

  /// 设备静音
  @ffi.Int()
  external int mute;
}

/// 音频设备引用类型
typedef AudioDeviceRef = ffi.Pointer<AudioDevice>;

/// 视频采集设备
final class VideoCaptureDevice extends ffi.Struct {
  /// 设备索引
  @ffi.Int()
  external int index;

  /// 设备路径
  external ffi.Pointer<ffi.Char> path;

  /// 设备名称
  external ffi.Pointer<ffi.Char> name;

  /// true 为 USB 扩展, false 为内置PCIE设备
  @ffi.Bool()
  external bool isUsbExtend;
}

/// 视频采集设备引用类型
typedef VideoCaptureDeviceRef = ffi.Pointer<VideoCaptureDevice>;

/// 主机设备磁盘空间信息
final class HostDeviceSpaceInfo extends ffi.Struct {
  /// 总容量
  @ffi.Uint64()
  external int capacity;

  /// 总剩余空间
  @ffi.Uint64()
  external int free;

  /// 当前用户配额下剩余空间
  @ffi.Uint64()
  external int available;
}

/// 主机设备磁盘空间信息引用类型
typedef HostDeviceSpaceInfoRef = ffi.Pointer<HostDeviceSpaceInfo>;

/// 纹理渲染器引用类型
typedef TextureRendererRef = ffi.Pointer<ffi.Void>;

/// 监视器引用类型
typedef MonitorRef = ffi.Pointer<ffi.Void>;

/// 视频录制信息
final class VideoRecordInfo extends ffi.Struct {
  /// 名称
  external ffi.Pointer<ffi.Char> name;

  /// 存储路径, 为空代表失败
  external ffi.Pointer<ffi.Char> path;

  /// 首个 I 帧, 偏移量
  @ffi.Int64()
  external int pointOffset;

  /// 总帧数
  @ffi.Int64()
  external int frameCount;
}

/// 视频录制信息引用类型
typedef VideoRecordInfoRef = ffi.Pointer<VideoRecordInfo>;

/// 录制器引用类型
typedef RecorderRef = ffi.Pointer<ffi.Void>;

/// 用户离开原因
///
/// - [quit] 退出
/// - [offline] 掉线
/// - [roleChange] 角色改变
enum RtcLiveUserLeftReason {
  /// 退出
  quit(0),

  /// 掉线
  offline(1),

  /// 角色改变
  roleChange(2);

  final int value;
  const RtcLiveUserLeftReason(this.value);

  static RtcLiveUserLeftReason fromValue(int value) => switch (value) {
    0 => quit,
    1 => offline,
    2 => roleChange,
    _ => throw ArgumentError('Unknown value for RtcLiveUserLeftReason: $value'),
  };
}

/// RTC 状态
final class RtcStats extends ffi.Struct {
  /// 频道用户数
  @ffi.Int()
  external int userCount;

  /// 视频发送码率, 单位: kbps
  @ffi.Int()
  external int sendVideoBitrate;

  /// 发送丢包率
  @ffi.Int()
  external int sendLossRate;

  /// 系统 CPU 使用率 %
  @ffi.Int()
  external int cpuTotalUsage;

  /// 系统内存使用率 %
  @ffi.Int()
  external int memoryTotalUsage;

  /// 客户端到本地路由器的往返延时
  @ffi.Int()
  external int gatewayRtt;
}

/// RTC 状态引用类型
typedef RtcStatsRef = ffi.Pointer<RtcStats>;

/// RTC 直播引用类型
typedef RtcLiveRef = ffi.Pointer<ffi.Void>;

/// RTMP 直播引用类型
typedef RtmpLiveRef = ffi.Pointer<ffi.Void>;

/// 语音命令状态
///
/// - [success] 识别成功
/// - [disconnect] 连接丢失
/// - [unRecognize] 未识别
enum VoiceCommandState {
  /// 识别成功
  success(0),

  /// 未识别
  unRecognize(1),

  /// 连接丢失
  disconnect(2);

  final int value;
  const VoiceCommandState(this.value);

  static VoiceCommandState fromValue(int value) => switch (value) {
    0 => success,
    1 => unRecognize,
    2 => disconnect,
    _ => throw ArgumentError('Unknown value for VoiceCommandState: $value'),
  };
}

/// 语音命令
final class VoiceCommand extends ffi.Struct {
  /// 语音状态
  @ffi.Uint32()
  external int stateAsInt;

  VoiceCommandState get state => VoiceCommandState.fromValue(stateAsInt);

  /// 语音内容
  external ffi.Pointer<ffi.Char> content;
}

/// 语音命令引用类型
typedef VoiceCommandRef = ffi.Pointer<VoiceCommand>;

/// 拷贝状态
///
/// - [copying] 拷贝中
/// - [success] 成功
/// - [failed] 失败
/// - [canceled] 已取消
enum CopyFileState {
  /// 拷贝中
  copying(0),

  /// 成功
  success(1),

  /// 失败
  failed(2),

  /// 已取消
  canceled(3);

  final int value;
  const CopyFileState(this.value);

  static CopyFileState fromValue(int value) => switch (value) {
    0 => copying,
    1 => success,
    2 => failed,
    3 => canceled,
    _ => throw ArgumentError('Unknown value for CopyFileState: $value'),
  };
}

/// 拷贝信息
final class CopyFileInfo extends ffi.Struct {
  /// 目标文件路径
  external ffi.Pointer<ffi.Char> dst;

  /// 拷贝状态
  @ffi.Int32()
  external int stateAsInt;

  CopyFileState get state => CopyFileState.fromValue(stateAsInt);

  /// 进度, 0.0 ~ 1.0
  @ffi.Double()
  external double progress;

  /// 速度, 字节/秒
  @ffi.Int64()
  external int speed;

  /// 剩余时间, 毫秒
  @ffi.Int64()
  external int remainingTime;

  /// 错误信息
  external ffi.Pointer<ffi.Char> error;
}

/// 拷贝信息引用类型
typedef CopyFileInfoRef = ffi.Pointer<CopyFileInfo>;

/// 传输数据类型
final class TransferData extends ffi.Struct {
  /// 数据ID
  external ffi.Pointer<ffi.Char> id;

  /// 数据
  external ffi.Pointer<ffi.Uint8> value;

  /// 数据长度
  @ffi.Int64()
  external int len;

  /// 行宽, 为0表示无行宽
  @ffi.Int64()
  external int stride;

  /// 行高, 为0表示无高度
  @ffi.Int64()
  external int height;
}

/// 传输数据引用类型
typedef TransferDataRef = ffi.Pointer<TransferData>;
