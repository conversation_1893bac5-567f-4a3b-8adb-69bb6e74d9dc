import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'medias_kit_ffi.dart' as ffi;
import 'medias_kit_module.dart';
import 'medias_kit_platform_interface.dart';

/// An implementation of [MediasKitPlatform] that uses method channels.
class MethodChannelMediasKit extends MediasKitPlatform {
  MethodChannelMediasKit() {
    methodChannel.setMethodCallHandler((call) async {
      final params = call.method.split(".");
      final module = params.first;
      final eventID = int.parse(params.last);
      debugPrint("$module.$eventID");
      switch (module) {
        default:
          throw MissingPluginException("Not implementation!");
      }
    });
  }

  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('medias_kit');

  //********************************** Module Core **********************************/
  @override
  Future<String?> bindNativePlugin() async {
    return methodChannel.invokeMethod<String>(
      Module.core.method(ffi.CoreEvent.bindNativePlugin.index),
    );
  }

  @override
  Future<void> bindDartApiDL(int apiPointer, int sendPort) async {
    return methodChannel.invokeMethod<void>(
      Module.core.method(ffi.CoreEvent.bindDartApiDL.index),
      {"apiPointer": apiPointer, "sendPort": sendPort},
    );
  }
}
