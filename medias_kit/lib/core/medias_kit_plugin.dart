import 'dart:async';
import 'dart:ffi' as ffi;
import 'dart:isolate';

import 'package:flutter/foundation.dart';

import '../core/medias_kit_types.dart';
import '../core/medias_kit_platform_interface.dart';
import '../device/host_device.dart';
import '../helper/helper.dart';
import '../recorder/recorder.dart';
import '../rtc_live/rtc_live.dart';
import '../rtmp_live/rtmp_live.dart';
import '../voice/voice_helper.dart';
import 'medias_kit_ffi.dart' as ffi;

/// 媒体库
class MediasKit {
  MediasKit._();

  static final _recvPort = ReceivePort();

  static String _pluginId = "";

  /// 插件 ID
  static String get pluginId {
    if (_pluginId.isEmpty) {
      throw Exception("MediasKit not initialized");
    }
    return _pluginId;
  }

  /// 插件初始化, 必须在使用任何功能之前调用一次
  static Future<void> ensureInitialized() async {
    _pluginId = (await MediasKitPlatform.instance.bindNativePlugin())!;
    await MediasKitPlatform.instance.bindDartApiDL(
      ffi.NativeApi.initializeApiDLData.address,
      _recvPort.sendPort.nativePort,
    );
    _recvPort.listen((data) {
      assert(data is int, "message must be Dart_CObject_kNativePointer type!");
      final message = ffi.Pointer<ffi.NativeMessage>.fromAddress(
        data,
      ).refWithFinalizer(
        ffi.addresses.nativeMessageDestroyWithoutValuePtr.cast(),
      );

      switch (message.event) {
        case ffi.NativeEvent.videoCapturedDeviceChanged:
          final videoCaptureDevices = message.value.as_ptr;
          final size = ffi.vectorSize(videoCaptureDevices);
          final videoSources = <ffi.VideoCaptureDevice>[];
          for (var i = 0; i < size; i++) {
            final device = ffi
                .vectorElementAt(videoCaptureDevices, i)
                .cast<ffi.VideoCaptureDevice>()
                .refWithFinalizer(
                  ffi.addresses.videoCaptureDeviceDestroy.cast(),
                );
            videoSources.add(device);
          }
          HostDevice.share.videoSources.value = videoSources;
          ffi.vectorDestroy(videoCaptureDevices, ffi.nullptr);
          break;

        case ffi.NativeEvent.audioSourceDeviceChanged:
          final audioSourceDevices = message.value.as_ptr;
          final size = ffi.vectorSize(audioSourceDevices);
          final audioSources = <ffi.AudioDevice>[];
          for (var i = 0; i < size; i++) {
            final device = ffi
                .vectorElementAt(audioSourceDevices, i)
                .cast<ffi.AudioDevice>()
                .refWithFinalizer(ffi.addresses.audioDeviceDestroy.cast());
            audioSources.add(device);
          }
          HostDevice.share.audioSources.value = audioSources;
          ffi.vectorDestroy(audioSourceDevices, ffi.nullptr);
          break;

        case ffi.NativeEvent.audioSinkDeviceChanged:
          final audioSinkDevices = message.value.as_ptr;
          final size = ffi.vectorSize(audioSinkDevices);
          final audioSinks = <ffi.AudioDevice>[];
          for (var i = 0; i < size; i++) {
            final device = ffi
                .vectorElementAt(audioSinkDevices, i)
                .cast<ffi.AudioDevice>()
                .refWithFinalizer(ffi.addresses.audioDeviceDestroy.cast());
            audioSinks.add(device);
          }
          HostDevice.share.audioSinks.value = audioSinks;
          ffi.vectorDestroy(audioSinkDevices, ffi.nullptr);
          break;

        case ffi.NativeEvent.videoMerged:
          final videoRecordInfo = message.value.as_ptr
              .cast<ffi.VideoRecordInfo>()
              .refWithFinalizer(ffi.addresses.videoRecordInfoDestroy.cast());
          Recorder.videoMerged(message.extra, videoRecordInfo);
          break;

        case ffi.NativeEvent.videoSignalStatusChanged:
          final hasSignal = message.value.as_bool;
          Recorder.videoSignalStatusChanged(message.extra, hasSignal);
          break;

        case ffi.NativeEvent.rtcLiveUserJoined:
          final userId = message.value.as_int;
          RtcLive.userJoined(message.extra, userId);
          break;

        case ffi.NativeEvent.rtcLiveUserLeft:
          final userIdOrReason = message.value.as_int;
          RtcLive.userLeft(message.extra, userIdOrReason);
          break;

        case ffi.NativeEvent.rtcLiveTransportStats:
          final rtcStats = message.value.as_ptr
              .cast<ffi.RtcStats>()
              .refWithFinalizer(ffi.addresses.rtcStatsDestroy.cast());
          RtcLive.transportStats(message.extra, rtcStats);
          break;

        case ffi.NativeEvent.rtcLiveNetworkQuality:
          final quality = message.value.as_int;
          RtcLive.networkQuality(message.extra, quality);
          break;

        case ffi.NativeEvent.rtcLiveNetworkType:
          final type = message.value.as_int;
          RtcLive.networkType(message.extra, type);
          break;

        case ffi.NativeEvent.rtcLiveReInit:
          RtcLive.reInit(message.extra);
          break;

          case ffi.NativeEvent.rtmpLiveVideoHandler:
          final transferData = message.value.as_ptr
              .cast<ffi.TransferData>()
              .refWithFinalizer(ffi.addresses.transferDataDestroy.cast());
          RtmpLive.videoHandler(message.extra, transferData);
          break;

        case ffi.NativeEvent.voiceHelperAwakened:
          VoiceHelper.share.onAwakened();
          break;

        case ffi.NativeEvent.voiceHelperRecognizing:
          final commandWords = message.value.as_ptr.cast<ffi.Char>().dartString;
          VoiceHelper.share.onRecognizing(commandWords);
          break;

        case ffi.NativeEvent.voiceHelperCommandParsed:
          final command = message.value.as_ptr
              .cast<ffi.VoiceCommand>()
              .refWithFinalizer(ffi.addresses.voiceCommandDestroy.cast());
          VoiceHelper.share.onCommand(command);
          break;

        case ffi.NativeEvent.fileCopy:
          final copyFileInfo = message.value.as_ptr
              .cast<ffi.CopyFileInfo>()
              .refWithFinalizer(ffi.addresses.copyFileInfoDestroy.cast());
          Helper.share.onCopyFile(copyFileInfo);
          break;

        case ffi.NativeEvent.dataTransfer:
          final transferData = message.value.as_ptr
              .cast<ffi.TransferData>()
              .refWithFinalizer(ffi.addresses.transferDataDestroy.cast());
          Helper.share.onDataTransfer(transferData);
          break;

        case ffi.NativeEvent.test:
          debugPrint("+++++++++++ ${message.value.as_int}");
          break;
      }
    });
  }
}
