/// 设备事件
enum DeviceEvent {
  getPlatform,
  getVideoCaptures,
  getSpaceInfo,
  geSpaceInfoWithPath,
  setAudioSourceVolume,
  setAudioSinkVolume,
  setOnline,
}

/// 监视控制器事件
enum MonitorControllerEvent { init, start, stop, dispose }

/// 模型控制器事件
enum ModelControllerEvent { init, dispose }

/// 录制控制器事件
enum RecordControllerEvent {
  init,
  start,
  stop,
  recordAudio,
  changeAudioSource,
  merge,
  frameCount,
  firstFrameTimestampWithOffset,
  dispose,
}

/// 会诊直播控制器事件
enum RtcLiveControllerEvent {
  init,
  start,
  stop,
  speak,
  changeAudioSource,
  changeAudioSink,
  dispose,
}

/// CDN直播控制器事件
enum RtmpLiveControllerEvent {
  init,
  start,
  stop,
  speak,
  changeAudioSource,
  dispose,
}

/// BLE外设控制器事件
enum PeripheralControllerEvent { init, start, stop, notify, dispose }

/// 语音助手事件
enum VoiceHelperEvent { prepare, disable, speaking, waitingCommand }

/// 模块对应 c++ 层模块
class Module {
  /// 核心模块
  static final core = Module("Core");

  /// 模块名称
  final String name;

  /// 构造器
  Module(this.name);

  /// 构造模块消息事件方法
  String method(int eventID) => "$name.$eventID";
}
