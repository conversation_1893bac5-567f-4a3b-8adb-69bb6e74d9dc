import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'medias_kit_method_channel.dart';

abstract class MediasKitPlatform extends PlatformInterface {
  /// Constructs a MediasKitPlatform.
  MediasKitPlatform() : super(token: _token);

  static final Object _token = Object();

  static MediasKitPlatform _instance = MethodChannelMediasKit();

  /// The default instance of [MediasKitPlatform] to use.
  ///
  /// Defaults to [MethodChannelMediasKit].
  static MediasKitPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [MediasKitPlatform] when
  /// they register themselves.
  static set instance(MediasKitPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  //********************************** Module Core **********************************/
  /// 绑定原生插件
  /// 返回插件ID
  Future<String?> bindNativePlugin();

  /// 绑定 Dart ApiDL
  /// - [apiPointer] DartApiDL 地址
  /// - [sendPort] 发送端口
  Future<void> bindDartApiDL(int apiPointer, int sendPort);
}
