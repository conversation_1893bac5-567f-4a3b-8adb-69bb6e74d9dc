import 'dart:ffi' as ffi;
import 'dart:typed_data';

/// 扩展 ffi.Pointer<ffi.Uint8>
/// 用于操作共享内存
extension SharedBufferExtension on ffi.Pointer<ffi.Uint8> {
  /// 转换 BGRA 到 RGBA 格式并直接裁剪到 sharedBuffer
  ///
  /// - [sourceData] 原始数据
  /// - [targetWidth] 目标宽度
  /// - [targetHeight] 目标高度
  /// - 返回裁剪后的数据大小
  int convertAndCrop(Uint8List sourceData, int targetWidth, int targetHeight) {
    // 计算原始数据的每行字节数（包含填充）
    final sourceStride = sourceData.lengthInBytes ~/ targetHeight;

    // 目标数据大小
    final targetSize = targetWidth * targetHeight * 4;
    final targetBuffer = asTypedList(targetSize);

    // 逐行处理：转换颜色格式并裁剪
    for (int row = 0; row < targetHeight; row++) {
      final sourceRowStart = row * sourceStride;
      final targetRowStart = row * targetWidth * 4;

      // 处理这一行的每个像素
      for (int col = 0; col < targetWidth; col++) {
        final sourcePixelStart = sourceRowStart + col * 4;
        final targetPixelStart = targetRowStart + col * 4;

        // BGRA -> RGBA: 交换红蓝通道
        targetBuffer[targetPixelStart] =
            sourceData[sourcePixelStart + 2]; // R = B
        targetBuffer[targetPixelStart + 1] =
            sourceData[sourcePixelStart + 1]; // G = G
        targetBuffer[targetPixelStart + 2] =
            sourceData[sourcePixelStart]; // B = R
        targetBuffer[targetPixelStart + 3] =
            sourceData[sourcePixelStart + 3]; // A = A
      }
    }
    return targetSize;
  }
}
