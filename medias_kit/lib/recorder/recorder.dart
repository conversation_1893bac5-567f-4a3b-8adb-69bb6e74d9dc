import 'dart:ffi' as ffi;

import 'package:medias_kit/recorder/recorder_reverse_call_interface.dart';

import '../core/medias_kit_ffi.dart' as ffi;
import '../core/medias_kit_types.dart';
import '../device/host_device.dart';

/// 录制器
class Recorder extends RecorderReverseCallInterface {
  /// 录制器集合
  static final _recorders = <Recorder>{};

  /// 视频合并完成
  ///
  /// - [recorderAddress] 录制器地址
  ///
  /// - [videoRecordInfo] 视频录制信息
  static void videoMerged(
    int recorderAddress,
    ffi.VideoRecordInfo videoRecordInfo,
  ) {
    for (var recorder in _recorders) {
      if (recorder._recorder?.address == recorderAddress) {
        recorder.onMerged?.call(videoRecordInfo);
      }
    }
  }

  /// 视频信号状态改变
  ///
  /// - [recorderAddress] 录制器地址
  ///
  /// - [hasSignal] 是否有视频信号
  static void videoSignalStatusChanged(int recorderAddress, bool hasSignal) {
    for (var recorder in _recorders) {
      if (recorder._recorder?.address == recorderAddress) {
        recorder.hasSignal.value = hasSignal;
      }
    }
  }

  /// 关联名称, 文件域名称
  final String spaceName;

  /// 视频采集设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final ffi.VideoCaptureDevice videoCaptureDevice;

  /// 音频输入源名称
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 采集宽度
  final int width;

  /// 采集高度
  final int height;

  /// 采集帧率
  final int framerate;

  /// 视频编码码率
  final int videoBitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频编码码率
  final int audioBitrate;

  /// 音频通道数
  final int numChannels;

  /// 是否初始化
  bool _isInit = false;

  /// 是否录制音频
  bool _enableAudioRecord = false;

  /// 原生录制器引用
  ffi.RecorderRef? _recorder;
  ffi.RecorderRef? get nativeRecorder => _recorder;

  /// 构造函数
  ///
  /// - [spaceName] 必传, 业务上为手术ID
  ///
  /// - [videoCaptureDevice] 必传
  ///
  /// - [audioSourceName] 默认值: default
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [videoBitrate] 默认值: 4096kbps
  ///
  /// - [sampleRate] 默认值: 48000Hz
  ///
  /// - [audioBitrate] 默认值: 96000bps
  ///
  /// - [numChannels] 默认值: 1
  Recorder({
    required this.spaceName,
    required this.videoCaptureDevice,
    String audioSourceName = "default",
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.videoBitrate = 4096,
    this.sampleRate = 48000,
    this.audioBitrate = 96000,
    this.numChannels = 1,
    super.onMerged,
  }) : _audioSourceName = audioSourceName {
    _recorders.add(this);
  }

  /// 初始化资源
  bool init() {
    if (_isInit) return true;
    final owner = Object();
    _recorder = ffi.recorderCreate(
      spaceName.toNativeCharPointer(owner),
      videoCaptureDevice.address,
      audioSourceName.toNativeCharPointer(owner),
      width,
      height,
      framerate,
      videoBitrate,
      sampleRate,
      audioBitrate,
      numChannels,
    );
    _isInit = ffi.recorderInit(_recorder!);
    return _isInit;
  }

  /// 开始
  void start() {
    if (_isInit) {
      ffi.recorderStart(_recorder!);
    }
  }

  /// 停止
  void stop() {
    if (_isInit) {
      ffi.recorderStop(_recorder!);
    }
  }

  /// 获取是否允许录制音频
  bool get enableAudioRecord => _enableAudioRecord;

  /// 允许录制音频
  void recordAudio(bool enable) {
    if (_isInit) {
      _enableAudioRecord = enable;
      ffi.recorderRecordAudio(_recorder!, enable);
    }
  }

  /// 视频合并, 返回合并后的文件路径
  ///
  /// - [name] 文件名
  ///
  /// - [beginTime] 开始时间戳
  ///
  /// - [endTime] 结束时间戳
  ///
  /// - [needEndPrecision] 是否需要精确结束时间帧, false: 结束帧会自动调整为关键帧前一帧
  void merge({
    required String name,
    required int beginTime,
    required int endTime,
    required bool needEndPrecision,
  }) {
    if (_isInit) {
      final owner = Object();
      ffi.recorderMerge(
        _recorder!,
        name.toNativeCharPointer(owner),
        beginTime,
        endTime,
        needEndPrecision,
      );
    }
  }

  /// 当前录制视频帧总数
  int frameCount() {
    if (_isInit) {
      return ffi.recorderFrameCount(_recorder!);
    }
    return 0;
  }

  /// 当前录制视频的首帧时间戳+错误偏移
  int firstFrameTimestampWithOffset() {
    if (_isInit) {
      return ffi.recorderFirstFrameTimestampWithOffset(_recorder!);
    }
    return 0;
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  void changeAudioSource({String name = "default"}) {
    if (_isInit) {
      _audioSourceName = name;
      final owner = Object();
      ffi.recorderUpdateAudioCapture(
        _recorder!,
        name.toNativeCharPointer(owner),
      );
    }
  }

  /// 释放资源, 标志结束录制
  void dispose() {
    _isInit = false;
    if (_recorder != null) {
      ffi.recorderDestroy(_recorder!);
    }
    _recorder = null;
    _recorders.remove(this);
  }
}
