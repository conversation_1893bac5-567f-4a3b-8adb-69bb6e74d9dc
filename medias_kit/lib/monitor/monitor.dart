import 'dart:ffi';

import 'package:flutter/material.dart';

import '../assets/shader_assets.dart';
import '../core/medias_kit_ffi.dart' as ffi;
import '../core/medias_kit_types.dart';
import '../core/medias_kit_plugin.dart';
import '../device/host_device.dart';

class MonitorState {
  /// OpenGL 纹理 ID
  final int textureId;

  final bool isLoading;

  final bool isFailure;

  final bool isSuccess;

  const MonitorState.loading()
    : textureId = 0,
      isLoading = true,
      isFailure = false,
      isSuccess = false;

  const MonitorState.failure()
    : textureId = 0,
      isLoading = false,
      isFailure = true,
      isSuccess = false;

  const MonitorState.success({required this.textureId})
    : isLoading = false,
      isFailure = false,
      isSuccess = true;
}

/// 画面采集监视控制器
class Monitor {
  /// 视频采集设备的名称, [HostDevice.videoSources] 中获取
  final ffi.VideoCaptureDevice videoCaptureDevice;

  /// 采集宽度
  final int width;

  /// 采集高度
  final int height;

  /// 采集帧率
  final int framerate;

  /// 原生监视器引用
  ffi.MonitorRef? _monitor;

  /// 构造函数
  ///
  /// - [videoCaptureDevice]
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 60
  Monitor({
    required this.videoCaptureDevice,
    this.width = 1920,
    this.height = 1080,
    this.framerate = 60,
  });

  var _state = const MonitorState.loading();

  /// 获取当前状态
  MonitorState get state => _state;

  /// 设置当前状态
  set state(MonitorState value) {
    _state = value;
    isNeedRefresh.value = true;
  }

  ShaderAssets? _shader;

  /// 获取着色器资源
  ShaderAssets? get shader => _shader;

  /// 设置着色器资源
  set shader(ShaderAssets? value) {
    _shader = value;
    isNeedRefresh.value = true;
  }

  /// 需要刷新 UI
  final isNeedRefresh = ValueNotifier<bool>(false);

  /// 初始化资源
  bool init() {
    if (state.isSuccess) return true;
    _monitor = ffi.monitorCreate();
    final owner = Object();
    final textureId = ffi.monitorInit(
      _monitor!,
      MediasKit.pluginId.toNativeCharPointer(owner),
      videoCaptureDevice.address,
      width,
      height,
      framerate,
    );
    if (textureId != 0) {
      state = MonitorState.success(textureId: textureId);
    } else {
      state = const MonitorState.failure();
    }
    return state.isSuccess;
  }

  /// 开始
  void start() {
    if (state.isSuccess) {
      return ffi.monitorStart(_monitor!);
    }
  }

  /// 停止
  void stop() {
    if (state.isSuccess) {
      return ffi.monitorStop(_monitor!);
    }
  }

  /// 释放资源
  void dispose() {
    isNeedRefresh.dispose();
    if (_monitor != null) {
      ffi.monitorDestroy(_monitor!);
    }
    _monitor = null;
  }
}
