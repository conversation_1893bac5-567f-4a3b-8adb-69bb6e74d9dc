import '../core/medias_kit_ffi.dart' as ffi;
import '../core/medias_kit_plugin.dart';
import '../core/medias_kit_types.dart';

class Helper {
  Helper._();

  static final share = Helper._();

  final _copyFileCallbacks = <String, void Function(ffi.CopyFileInfo)>{};

  /// 拷贝文件回调, 非原生事件不要调用
  void onCopyFile(ffi.CopyFileInfo info) {
    _copyFileCallbacks[info.dst.dartString]?.call(info);
    if (info.state != ffi.CopyFileState.copying) {
      _copyFileCallbacks.remove(info.dst.dartString);
    }
  }

  void copyFile({
    required String src,
    required String dst,
    required void Function(ffi.CopyFileInfo) callback,
  }) {
    _copyFileCallbacks[dst] = callback;
    final owner = Object();
    ffi.copyFile(
      src.toNativeCharPointer(owner),
      dst.toNativeCharPointer(owner),
      MediasKit.pluginId.toNativeCharPointer(owner),
    );
  }

  void cancelTask(String dst) {
    final owner = Object();
    ffi.cancelTask(dst.toNativeCharPointer(owner));
  }

  final _dataTransferCallbacks = <String, void Function(ffi.TransferData)>{};

  /// 数据传输回调, 非原生事件不要调用
  void onDataTransfer(ffi.TransferData data) {
    _dataTransferCallbacks[data.id.dartString]?.call(data);
  }

  /// 订阅数据传输, 模式为每个id具有唯一订阅
  void subscribeDataTransfer({
    required String id,
    required void Function(ffi.TransferData) callback,
  }) {
    _dataTransferCallbacks[id] = callback;
  }
}
