import 'dart:ffi' as ffi;

import 'package:medias_kit/medias_kit.dart';

import '../core/medias_kit_ffi.dart' as ffi;
import 'rtc_live_reverse_call_interface.dart';

/// 会诊直播器
class RtcLive extends RtcLiveReverseCallInterface {
  /// 会诊直播器集合
  static final _rtcLives = <RtcLive>{};

  /// 用户加入
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  ///
  /// - [userId] 用户ID
  static void userJoined(int rtcLiveAddress, int userId) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onUserJoined?.call(userId);
      }
    }
  }

  /// 用户离开
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  ///
  /// - [userIdOrReason] 用户ID | 离开原因 << 32
  static void userLeft(int rtcLiveAddress, int userIdOrReason) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onUserLeft?.call(
          userIdOrReason & 0xFFFFFFFF,
          ffi.RtcLiveUserLeftReason.fromValue(userIdOrReason >> 32),
        );
      }
    }
  }

  /// 传输状态
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  ///
  /// - [rtcStats] 传输状态
  static void transportStats(int rtcLiveAddress, ffi.RtcStats rtcStats) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onTransportStats?.call(rtcStats);
      }
    }
  }

  /// 网络质量
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  ///
  /// - [quality] 网络质量
  static void networkQuality(int rtcLiveAddress, int quality) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onNetworkQuality?.call(quality);
      }
    }
  }

  /// 网络类型
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  ///
  /// - [type] 网络类型
  static void networkType(int rtcLiveAddress, int type) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onNetworkType?.call(type);
      }
    }
  }

  /// 重新初始化
  ///
  /// - [rtcLiveAddress] 会诊直播器地址
  static void reInit(int rtcLiveAddress) {
    for (var rtcLive in _rtcLives) {
      if (rtcLive._rtcLive?.address == rtcLiveAddress) {
        rtcLive.onReInit?.call();
      }
    }
  }

  /// 流类型
  final ffi.StreamType streamType;

  /// 声网应用 ID
  final String appId;

  /// 声网鉴权 token
  final String token;

  /// 声网频道 ID
  final String channelId;

  /// 声网用户 ID
  final String userId;

  /// 视频采集设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final ffi.VideoCaptureDevice videoCaptureDevice;

  /// 音频输入源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 音频输出源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSinks] 中获取
  String get audioSinkName => _audioSinkName;
  String _audioSinkName;

  /// 音频输出音量
  double get audioSinkVolume => _audioSinkVolume;
  double _audioSinkVolume;

  /// 视频采集宽度
  final int width;

  /// 视频采集高度
  final int height;

  /// 视频采集帧率
  final int framerate;

  /// 视频编码码率
  final int bitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频通道数量
  final int numChannels;

  /// 是否初始化
  bool _isInit = false;

  /// 是否允许说话
  bool _enableSpeak = false;

  /// Rtc 直播器引用
  ffi.RtcLiveRef? _rtcLive;

  /// 构造函数
  ///
  /// - [videoCaptureDevice] 默认值: 缺省
  ///
  /// - [audioSourceName] 默认值: default
  ///
  /// - [audioSinkName] 默认值: default
  ///
  /// - [audioSinkVolume] 默认值: 1.0
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [bitrate] 默认值: 0kbps
  ///
  /// - [sampleRate] 默认值: 48000
  ///
  /// - [numChannels] 默认值: 1
  RtcLive({
    required this.streamType,
    required this.appId,
    required this.token,
    required this.channelId,
    required this.userId,
    required this.videoCaptureDevice,
    String audioSourceName = "default",
    String audioSinkName = "default",
    double audioSinkVolume = 1.0,
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.bitrate = 0,
    this.sampleRate = 48000,
    this.numChannels = 1,
    super.onUserJoined,
    super.onUserLeft,
    super.onTransportStats,
    super.onNetworkQuality,
    super.onNetworkType,
    super.onReInit,
  }) : _audioSourceName = audioSourceName,
       _audioSinkName = audioSinkName,
       _audioSinkVolume = audioSinkVolume {
    _rtcLives.add(this);
  }

  /// 初始化资源
  bool init() {
    if (_isInit) return true;
    final owner = Object();
    _rtcLive = ffi.rtcLiveCreate(
      streamType.name.toNativeCharPointer(owner),
      appId.toNativeCharPointer(owner),
      token.toNativeCharPointer(owner),
      channelId.toNativeCharPointer(owner),
      int.parse(userId),
      videoCaptureDevice.address,
      audioSourceName.toNativeCharPointer(owner),
      audioSinkName.toNativeCharPointer(owner),
      (audioSinkVolume * 1.8).toInt(),
      width,
      height,
      framerate,
      bitrate,
      sampleRate,
      numChannels,
    );
    _isInit = ffi.rtcLiveInit(_rtcLive!);
    return _isInit;
  }

  void attachRemoteAudioHandler({
    ffi.RecorderRef? nativeRecorder,
    ffi.RtmpLiveRef? nativeRtmpLive,
  }) {
    if (_isInit) {
      ffi.attachRemoteAudioHandler(
        _rtcLive!,
        nativeRecorder ?? ffi.nullptr,
        nativeRtmpLive ?? ffi.nullptr,
      );
    }
  }

  /// 开始
  void start() {
    if (_isInit) {
      ffi.rtcLiveStart(_rtcLive!);
    }
  }

  /// 停止
  void stop() {
    if (_isInit) {
      ffi.rtcLiveStop(_rtcLive!);
    }
  }

  /// 获取是否允许说话状态
  bool get enableSpeak => _enableSpeak;

  /// 设置是否允许说话
  ///
  /// - [enable] 是否允许说话
  void speak(bool enable) {
    if (_isInit) {
      _enableSpeak = enable;
      ffi.rtcLiveSpeak(_rtcLive!, enable);
    }
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  void changeAudioSource({String name = "default"}) {
    if (_isInit) {
      _audioSourceName = name;
      final owner = Object();
      ffi.rtcLiveChangeAudioSource(_rtcLive!, name.toNativeCharPointer(owner));
    }
  }

  /// 切换音频输出源
  ///
  /// - [name] 输出源名称, 缺省: default
  ///
  /// - [volume] 输出音量, 缺省: 1.0
  void changeAudioSink({String name = "default", double volume = 1.0}) {
    if (_isInit) {
      _audioSinkName = name;
      _audioSinkVolume = volume;

      /// 扩音1.8倍
      final owner = Object();
      ffi.rtcLiveChangeAudioSink(
        _rtcLive!,
        name.toNativeCharPointer(owner),
        volume * 1.8,
      );
    }
  }

  /// 释放资源, 标志结束直播
  void dispose() {
    _isInit = false;
    _enableSpeak = false;
    if (_rtcLive != null) {
      ffi.rtcLiveDestroy(_rtcLive!);
    }
    _rtcLive = null;
    _rtcLives.remove(this);
  }
}
