import '../core/medias_kit_ffi.dart' as ffi;

/// 直播反向调用接口
abstract class RtcLiveReverseCallInterface {
  /// 用户加入频道
  final void Function(int userId)? onUserJoined;

  /// 用户离开频道
  final void Function(int userId, ffi.RtcLiveUserLeftReason reason)? onUserLeft;

  /// 传输状态
  final void Function(ffi.RtcStats rtcStats)? onTransportStats;

  /// 网络质量
  final void Function(int quality)? onNetworkQuality;

  /// 网络类型
  final void Function(int type)? onNetworkType;

  /// RTC连接丢失, 需要重新初始化
  final void Function()? onReInit;

  /// 构造函数
  ///
  /// - [onUserJoined] 默认值: 缺省
  ///
  /// - [onUserLeft] 默认值: 缺省
  ///
  /// - [onTransportStats] 默认值: 缺省
  ///
  /// - [onNetworkQuality] 默认值: 缺省
  ///
  /// - [onNetworkType] 默认值: 缺省
  RtcLiveReverseCallInterface({
    this.onUserJoined,
    this.onUserLeft,
    this.onTransportStats,
    this.onNetworkQuality,
    this.onNetworkType,
    this.onReInit,
  });
}
