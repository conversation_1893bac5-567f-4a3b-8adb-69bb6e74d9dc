import 'dart:io';

import '../core/medias_kit_ffi.dart' as ffi;
import '../core/medias_kit_types.dart';

/// 语音任务状态
enum VoiceTaskState {
  /// 结束
  done,

  /// 继续
  next,

  /// 重试
  retry,

  /// 返回
  back,

  /// 中断
  cancel,
}

/// 语音任务结果
class VoiceTaskResult {
  /// 任务执行状态
  final VoiceTaskState state;

  /// 提示信息
  final String remindWords;

  const VoiceTaskResult({
    this.state = VoiceTaskState.done,
    this.remindWords = "",
  });
}

/// 任务执行器
///
/// - [command] 语音命令
///
/// - [VoiceTaskResult] 任务执行结果
typedef VoiceTaskRunner = Future<VoiceTaskResult> Function(String command);

/// 任务取消器
typedef VoiceTaskCanceler = Future<void> Function();

/// 语音助手任务
class VoiceTask {
  /// 执行任务
  final VoiceTaskRunner run;

  /// 强制取消任务
  final VoiceTaskCanceler? cancel;

  VoiceTask(this.run, [this.cancel]);

  /// 上一步
  VoiceTask? _preTask;

  /// 下一步
  VoiceTask? _nextTask;

  /// 设置下一阶段任务, 不设置则中断此次任务流
  ///
  /// - [run] 下一步任务
  ///
  /// - [cancel] 强制取消下一步任务
  VoiceTask? nextTask(VoiceTaskRunner run, [VoiceTaskCanceler? cancel]) {
    var task = this;
    while (task._nextTask != null) {
      task = task._nextTask!;
    }
    task._nextTask = VoiceTask(run, cancel);
    task._nextTask?._preTask = task;
    return this;
  }
}

/// 语音助手处理器, 只处理第一层命令，如果需要处理更多任务流，请使用 VoiceTask
mixin VoiceHandler {
  /// 命中测试, 返回任务
  ///
  /// - [command] 语音命令
  VoiceTask? hitTest(String command);
}

/// 语音助手
class VoiceHelper {
  final _handlers = <VoiceHandler>{};

  /// 准备好的
  var _isPrepared = false;

  /// 错误回调
  void Function(ffi.VoiceCommandState state, String speakWords)? _onError;

  /// 唤醒回调
  void Function(String greetingWords)? _onAwakened;

  /// 识别回调
  void Function(String commandWords, bool finish)? _onRecognizing;

  /// 当前任务
  VoiceTask? _currentTask;

  /// 问候语
  var greetingWords = "我在";

  /// 抱歉语
  var sorryWords = "抱歉, 我不太明白";

  /// 离线提示语
  var offlineWords = "网络不太好, 小睿先退下了";

  /// 超时提示语
  var timeoutWords = "";

  VoiceHelper._();

  static final share = VoiceHelper._();

  /// 配置
  ///
  /// - [onError] 错误回调
  ///
  /// - [onAwakened] 唤醒回调
  ///
  /// - [onRecognizing] 识别回调
  void config({
    void Function(ffi.VoiceCommandState state, String speakWords)? onError,
    void Function(String greetingWords)? onAwakened,
    void Function(String commandWords, bool finish)? onRecognizing,
  }) {
    _onError = onError;
    _onAwakened = onAwakened;
    _onRecognizing = onRecognizing;
  }

  /// 添加命令处理器
  void addHandler(VoiceHandler handler) {
    _handlers.add(handler);
  }

  /// 移除命令处理器
  void removeHandler(VoiceHandler handler) {
    _handlers.remove(handler);
  }

  /// 语音助手准备就绪
  ///
  /// - [audioCaptureName] 音频采集设备名
  ///
  /// - [audioRenderName] 音频播放设备名
  ///
  /// - [renderVolume] 音量 0~1
  bool prepare({
    String audioCaptureName = "default",
    String audioRenderName = "default",
    double renderVolume = 1.0,
  }) {
    final pathSegments = Platform.resolvedExecutable.split("/")..removeLast();
    final owner = Object();
    _isPrepared = ffi.voiceHelperPrepare(
      pathSegments.join("/").toNativeCharPointer(owner),
      audioCaptureName.toNativeCharPointer(owner),
      audioRenderName.toNativeCharPointer(owner),
      renderVolume,
    );
    return _isPrepared;
  }

  /// 语音助手禁用
  void disable() {
    cancelTask();
    ffi.voiceHelperDisable();
    _isPrepared = false;
  }

  /// 语音播报
  ///
  /// - [words] 播报语句
  void speaking(String words) {
    if (words.isEmpty) return;
    // 使用 Unicode 正则表达式匹配所有标点符号
    final speakWords = words.replaceAll(RegExp(r'[^\w\s\u4e00-\u9fa5]'), '');
    final owner = Object();
    ffi.voiceHelperSpeaking(speakWords.toNativeCharPointer(owner));
  }

  /// 等待命令
  void waitingCommand() {
    ffi.voiceHelperWaitingCommand();
  }

  /// 取消所有任务
  Future<void> cancelTask() async {
    while (_currentTask != null) {
      await _currentTask!.cancel?.call();
      _currentTask = _currentTask?._preTask;
    }
  }

  /// 被唤醒
  void onAwakened() {
    if (!_isPrepared) {
      return;
    }
    // 当前任务优先级最高，不需要取消
    // await cancelTask();
    if (_currentTask != null) {
      return;
    }
    _onAwakened?.call(greetingWords);
    speaking(greetingWords);
    waitingCommand();
  }

  /// 识别实时回调
  void onRecognizing(String commandWords) {
    if (!_isPrepared) {
      return;
    }
    _onRecognizing?.call(commandWords, false);
  }

  /// 得到命令
  void onCommand(ffi.VoiceCommand command) async {
    if (!_isPrepared) {
      return;
    }
    if (ffi.VoiceCommandState.disconnect == command.state) {
      _onError?.call(command.state, offlineWords);
      speaking(offlineWords);
      await cancelTask();
      return;
    }
    final cmd = command.content.dartString;
    _onRecognizing?.call(cmd, true);
    if (cmd.isEmpty) {
      _onError?.call(command.state, timeoutWords);
      speaking(timeoutWords);
      await cancelTask();
      return;
    }
    if (_currentTask == null) {
      for (var handler in _handlers) {
        _currentTask = handler.hitTest(cmd);
        if (_currentTask != null) {
          _runCommand(cmd);
          return;
        }
      }
      speaking(sorryWords);
    } else {
      _runCommand(cmd);
    }
  }

  /// 执行命令
  void _runCommand(String command) async {
    final result = await _currentTask!.run(command);
    speaking(result.remindWords);
    switch (result.state) {
      case VoiceTaskState.done:
        _currentTask = null;
        break;
      case VoiceTaskState.next:
        _currentTask = _currentTask?._nextTask;
        waitingCommand();
        break;
      case VoiceTaskState.retry:
        waitingCommand();
        break;
      case VoiceTaskState.back:
        _currentTask = _currentTask?._preTask;
        waitingCommand();
        break;
      case VoiceTaskState.cancel:
        await cancelTask();
        break;
    }
  }
}
