import 'dart:ffi' as ffi;

import 'rtmp_live_reverse_call_interface.dart';
import '../core/medias_kit_ffi.dart' as ffi;
import '../core/medias_kit_types.dart';
import '../device/host_device.dart';

/// Rtmp 直播器
class RtmpLive extends RtmpLiveReverseCallInterface {
  /// Rtmp 直播器集合
  static final _rtmpLives = <RtmpLive>{};

  /// 视频数据处理
  ///
  /// - [data] 视频数据
  static void videoHandler(int rtmpLiveAddress, ffi.TransferData data) {
    for (var rtmpLive in _rtmpLives) {
      if (rtmpLive._rtmpLive?.address == rtmpLiveAddress) {
        rtmpLive.onVideoHandler?.call(data);
      }
    }
  }

  /// 流类型
  final ffi.StreamType streamType;

  /// 声网应用 ID
  final String appId;

  /// CDN推流地址
  final String url;

  /// 视频采集设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final ffi.VideoCaptureDevice videoCaptureDevice;

  /// 音频输入源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 视频采集宽度
  final int width;

  /// 视频采集高度
  final int height;

  /// 视频采集帧率
  final int framerate;

  /// 视频编码码率
  final int bitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频通道数量
  final int numChannels;

  /// 视频处理器插件ID, 为空表示所有的独立插件都会收到视频数据处理回调
  final String videoHandlerPluginId;

  /// 是否初始化
  bool _isInit = false;

  /// 是否允许说话
  bool _enableSpeak = false;

  /// Rtmp 直播器引用
  ffi.RtmpLiveRef? _rtmpLive;
  ffi.RtmpLiveRef? get nativeRtmpLive => _rtmpLive;

  /// 构造函数
  ///
  /// - [videoCaptureDevice]
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [bitrate] 默认值: 4096kbps
  ///
  /// - [sampleRate] 默认值: 48000
  ///
  /// - [numChannels] 默认值: 1
  ///
  /// - [videoHandlerPluginId] 默认值: ""
  RtmpLive({
    required this.streamType,
    required this.appId,
    required this.url,
    required this.videoCaptureDevice,
    String audioSourceName = 'default',
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.bitrate = 4096,
    this.sampleRate = 48000,
    this.numChannels = 1,
    this.videoHandlerPluginId = "",
    super.onVideoHandler,
  }) : _audioSourceName = audioSourceName {
    _rtmpLives.add(this);
  }

  /// 初始化资源
  bool init() {
    if (_isInit) return true;
    final owner = Object();
    _rtmpLive = ffi.rtmpLiveCreate(
      streamType.name.toNativeCharPointer(owner),
      appId.toNativeCharPointer(owner),
      url.toNativeCharPointer(owner),
      videoCaptureDevice.address,
      audioSourceName.toNativeCharPointer(owner),
      width,
      height,
      framerate,
      bitrate,
      sampleRate,
      numChannels,
      videoHandlerPluginId.toNativeCharPointer(owner),
      onVideoHandler != null,
    );
    _isInit = ffi.rtmpLiveInit(_rtmpLive!);
    return _isInit;
  }

  /// 开始
  void start() {
    if (_isInit) {
      ffi.rtmpLiveStart(_rtmpLive!);
    }
  }

  /// 停止
  void stop() {
    if (_isInit) {
      ffi.rtmpLiveStop(_rtmpLive!);
    }
  }

  /// 获取是否允许说话状态
  bool get enableSpeak => _enableSpeak;

  /// 设置是否允许说话
  ///
  /// - [enable]
  void speak(bool enable) {
    if (_isInit) {
      _enableSpeak = enable;
      ffi.rtmpLiveSpeak(_rtmpLive!, enable);
    }
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  void changeAudioSource({String name = "default"}) {
    if (_isInit) {
      _audioSourceName = name;
      final owner = Object();
      ffi.rtmpLiveChangeAudioSource(
        _rtmpLive!,
        name.toNativeCharPointer(owner),
      );
    }
  }

  void pushCustomRgbaVideo(
    ffi.Pointer<ffi.Uint8> frame,
    int frameLen,
    int width,
    int height,
  ) {
    if (_isInit) {
      ffi.rtmpLivePushCustomRgbaVideo(
        _rtmpLive!,
        frame,
        frameLen,
        width,
        height,
      );
    }
  }

  /// 释放资源, 标志结束直播
  void dispose() {
    _isInit = false;
    _enableSpeak = false;
    if (_rtmpLive != null) {
      ffi.rtmpLiveDestroy(_rtmpLive!);
    }
    _rtmpLive = null;
    _rtmpLives.remove(this);
  }
}
