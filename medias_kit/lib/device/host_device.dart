import 'dart:ffi' as ffi;

import 'package:flutter/foundation.dart';

import '../core/medias_kit_types.dart';
import '../core/medias_kit_ffi.dart' as ffi;

/// 主机设备
class HostDevice {
  HostDevice._();

  /// 单例
  static final share = HostDevice._();

  /// 音频输入源
  final audioSources = ValueNotifier<List<ffi.AudioDevice>>([]);

  /// 音频输出源
  final audioSinks = ValueNotifier<List<ffi.AudioDevice>>([]);

  /// 视频输入源
  final videoSources = ValueNotifier<List<ffi.VideoCaptureDevice>>([]);

  /// 获取运行环境平台信息
  String getPlatform() {
    return ffi.getHostDevicePlatform().dartString;
  }

  /// 获取主机设备磁盘空间信息
  ///
  /// - [path] 磁盘路径
  ffi.HostDeviceSpaceInfo getSpaceInfo([String path = "/"]) {
    final owner = Object();
    return ffi
        .getHostDeviceSpaceInfo(path.toNativeCharPointer(owner))
        .refWithFinalizer(ffi.addresses.hostDeviceSpaceInfoDestroy.cast());
  }

  /// 开始监听视频输入源
  void startListenVideoCaptureDevices() {
    ffi.startListenVideoCaptureDevices();
  }

  /// 开始监听音频设备
  void startListenAudioDevices() {
    ffi.startListenAudioDevices();
  }

  /// 设置输入设备音量
  ///
  /// - [device] 音频设备
  /// - [volume] 音量 0~100
  void setAudioSourceVolume(ffi.AudioDevice device, int volume) {
    ffi.setAudioSourceVolume(device.address, volume);
  }

  /// 设置输出设备音量
  ///
  /// - [device] 音频设备
  /// - [volume] 音量 0~100
  void setAudioSinkVolume(ffi.AudioDevice device, int volume) {
    ffi.setAudioSinkVolume(device.address, volume);
  }

  /// 设置默认音频输出设备
  ///
  /// - [device] 音频设备
  void setDefaultAudioSink(ffi.AudioDevice device) {
    ffi.setDefaultAudioSink(device.address);
  }

  /// 设置默认音频输入设备
  ///
  /// - [device] 音频设备
  void setDefaultAudioSource(ffi.AudioDevice device) {
    ffi.setDefaultAudioSource(device.address);
  }

  /// 设置设备是否网络在线, 需根据网络状态及时更新
  ///
  /// - [isOnline] 是否网络在线
  void setOnline(bool isOnline) {
    ffi.setDeviceIsOnline(isOnline);
  }
}
