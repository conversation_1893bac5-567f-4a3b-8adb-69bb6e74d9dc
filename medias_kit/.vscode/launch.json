{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "medias_kit",
      "request": "launch",
      "type": "dart",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "medias_kit (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "medias_kit (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "example",
      "cwd": "example",
      "request": "launch",
      "type": "dart",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "example (profile mode)",
      "cwd": "example",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "example (release mode)",
      "cwd": "example",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "C/C++ Attach to Process with GDB",
      "type": "cppdbg",
      "request": "attach",
      "processId": "${command:pickProcess}",
      "program": "${workspaceFolder}/example/build/linux/x64/debug/bundle/medias_kit_example",
      "MIMode": "gdb",
      "setupCommands": [
        {
          "description": "为 gdb 启用整齐打印",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ],
      "miDebuggerPath": "/usr/bin/gdb",
      "preLaunchTask": "lower ptrace_scope"
    }
  ],
  "compounds": [
    {
      "name": "[DEBUG] Flutter + C/C++",
      "configurations": ["medias_kit", "C/C++ Attach to Process with GDB"],
      "stopAll": true
    }
  ]
}
