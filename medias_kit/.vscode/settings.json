{"cmake.sourceDirectory": "${workspaceFolder}/example/linux", "clangd.arguments": ["--background-index", "--compile-commands-dir=build", "--clang-tidy", "--all-scopes-completion", "--completion-style=detailed", "--header-insertion=iwyu", "--pch-storage=disk"], "cSpell.words": ["AACENC", "AACENCODER", "aikit", "aisound", "ansicolor", "aosl", "asound", "autofree", "autoptr", "BGRA", "Binc", "bluez", "bun<PERSON>", "CHANNELMODE", "Cupertino", "cvolume", "DCURRENT", "DENOISE", "DEREVERB", "Destory", "eabb", "fdkaac", "ffigen", "FOURCC", "fragsize", "FRAMEBUFFER", "g<PERSON>lean", "gchar", "gint", "glong", "gmock", "googletest", "gpointer", "gtest", "guint", "HTIMER", "<PERSON><PERSON><PERSON>", "IWYU", "kbps", "klass", "Lastmile", "libmedias", "libserialport", "lldb", "Lsizei", "LTWH", "malloc", "minreq", "MIPMAP", "MWCAP", "MWFOURCC", "nativewrappers", "nvenc", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "prebuf", "QISR", "QUERYCAP", "RATECONTROL", "readi", "refptr", "resolv", "Responsetime", "rpath", "Rtmp", "SAMPLERATE", "<PERSON><PERSON><PERSON>", "spdlog", "Speex", "speexdsp", "strdown", "Struct", "strv", "surgsmart", "syspath", "TARGETUSAGE", "tlength", "TRANSMUX", "udev", "uframes", "venc", "VIDIOC", "wdec", "webrtc", "wfst", "withai", "writei", "x<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "xrxr", "xtts"]}