import 'package:flutter_test/flutter_test.dart';
import 'package:medias_kit/core/medias_kit_method_channel.dart';
import 'package:medias_kit/core/medias_kit_platform_interface.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockMediasKitPlatform
    with MockPlatformInterfaceMixin
    implements MediasKitPlatform {
  @override
  Future<String?> bindNativePlugin() {
    throw UnimplementedError();
  }

  @override
  Future<void> bindDartApiDL(int apiPointer, int sendPort) {
    throw UnimplementedError();
  }
}

void main() {
  final MediasKitPlatform initialPlatform = MediasKitPlatform.instance;

  test('$MethodChannelMediasKit is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelMediasKit>());
  });

  test('getPlatformVersion', () async {
    MockMediasKitPlatform fakePlatform = MockMediasKitPlatform();
    MediasKitPlatform.instance = fakePlatform;

    expect(HostDevice.share.getPlatform(), 'Some error!');
  });
}
