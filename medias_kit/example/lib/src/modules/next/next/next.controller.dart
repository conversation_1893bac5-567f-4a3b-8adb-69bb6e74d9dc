import 'dart:ffi' as ffi;
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/material.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:screenshot/screenshot.dart';

/// 所属模块: next
///
/// 采集
class NextController extends AppController with StateMixin<ApiModel> {
  NextController(super.key, super.routerState);

  late final videoCaptureDevice = (routerState?.extra
      as Map<String, dynamic>)["item"] as ffi.VideoCaptureDevice;
  late final windowId =
      (routerState?.extra as Map<String, dynamic>)["windowId"] as int?;

  final screenshotController = ScreenshotController();

  late final render = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  final sharedBuffer = ffi.malloc<ffi.Uint8>(3840 * 2160 * 4);

  late final monitor = Monitor(
    videoCaptureDevice: videoCaptureDevice,
    width: 1920,
    height: 1080,
    framerate: 30,
  );

  late final recorder = Recorder(
    spaceName: "19709",
    videoCaptureDevice: videoCaptureDevice,
    videoBitrate: 4096,
    onMerged: (videoInfo) {
      app.logD(">>>>>>>>>>>>>>>>>merged");
      app.logD(videoInfo.toString());
    },
  );

  late final rtcLive = RtcLive(
    streamType: StreamType.main,
    videoCaptureDevice: videoCaptureDevice,
    appId: "44f6c4f99b414bbaa2302b03ea08c0b1",
    token:
        "007eJxTYFhbM+2qz2UtZp4za2f/TXh9oqFw4pbHriWnbm26snrP4lOLFBhMTNLMkk3SLC2TTAxNkpISE42MDYySDIxTEw0skg2SDFNjojIOrGBgqCvawsjIwMjAAsQgwAQmmcEkC5jkYMgNyE43LQ+wYGYwMjIHAMzmJKA=",
    channelId: "mPkg5wP8",
    userId: "227",
    onUserJoined: (userId) {
      // app.logD(">>>>>>>>>>>>>>>>>user joined: $userId");
    },
    onUserLeft: (userId, reason) {
      // app.logD(">>>>>>>>>>>>>>>>>user left: $userId, reason: $reason");
    },
    onTransportStats: (rtcStats) {
      app.logD(
        ">>>>>>>>>>>>>>>>>video bitrate: ${rtcStats.sendVideoBitrate}kbps, sendLossRate: ${rtcStats.sendLossRate}",
      );
    },
  );

  final colors = [
    Colors.red,
    Colors.green,
    Colors.blue,
    Colors.amber,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.indigo,
    Colors.lime,
    Colors.teal,
    Colors.brown,
    Colors.grey,
    Colors.black,
    Colors.white,
    Colors.yellow,
    Colors.blueGrey,
    Colors.deepOrange,
    Colors.deepPurple,
    Colors.lightBlue,
    Colors.lightGreen,
    Colors.lime,
    Colors.orange,
    Colors.pink,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.yellow,
    Colors.amber,
    Colors.blue,
  ];

  late final RtmpLive rtmpLive = RtmpLive(
      videoCaptureDevice: videoCaptureDevice,
      streamType: StreamType.main,
      appId: "44f6c4f99b414bbaa2302b03ea08c0b1",
      url: 'rtmp://push.surgsmart.com/live/10016',
      videoHandlerPluginId: MediasKit.pluginId,
      onVideoHandler: (data) {
        final pixels = data.value.asTypedList(data.len);
        ui.decodeImageFromPixels(
          pixels,
          data.stride,
          data.height,
          ui.PixelFormat.rgba8888,
          (image) async {
            final recorder = ui.PictureRecorder();
            final canvas = ui.Canvas(
              recorder,
              ui.Rect.fromLTWH(
                0,
                0,
                rtmpLive.width.toDouble(),
                rtmpLive.height.toDouble(),
              ),
            );
            canvas.drawImage(image, ui.Offset.zero, ui.Paint());

            final paint = ui.Paint()
              ..color = colors[Random().nextInt(colors.length - 1)]
              ..strokeWidth = 4
              ..style = PaintingStyle.stroke;
            final path = Path();
            for (var _ in List.generate(20, (index) => index)) {
              paint.color = colors[Random().nextInt(colors.length - 1)];
              final x = Random().nextDouble() * data.stride;
              final y = Random().nextDouble() * data.height;
              path.moveTo(x, y);
              path.lineTo(data.stride / 2, data.height / 2);
            }
            // path.close();
            canvas.drawPath(path, paint);

            final pic = recorder.endRecording();
            final img = await pic.toImage(data.stride, data.height);
            final bd = await img.toByteData(format: ui.ImageByteFormat.rawRgba);

            sharedBuffer
                .asTypedList(bd!.lengthInBytes)
                .setAll(0, bd.buffer.asUint8List());

            ffi.textureRendererRenderRgbaFrame(
              render,
              sharedBuffer,
              bd.lengthInBytes,
              img.width,
              img.height,
            );

            rtmpLive.pushCustomRgbaVideo(
              sharedBuffer,
              data.len,
              data.stride,
              data.height,
            );
          },
        );
      });

  Timer? timer;

  final image = ValueNotifier<ui.Image?>(null);

  ffi.TextureRendererRef? otherRender;

  @override
  void onInit() {
    if (windowId != null) {
      Future.delayed(const Duration(seconds: 2), () async {
        int address = await DesktopMultiWindow.invokeMethod(
          windowId!,
          "getRender",
        );
        print(">>>>>>>>>>> test window render address: $address");
        otherRender = ffi.TextureRendererRef.fromAddress(address);
      });
    }

    timer = Timer.periodic(Duration(milliseconds: (1000 / 25).toInt()),
        (timer) async {
      // final img = await screenshotController.captureAsUiImage();
      // image.value = img;
      // if (img == null) {
      //   return;
      // }

      // final data = await img.toByteData(
      //   format: ui.ImageByteFormat.rawRgba,
      // );

      // sharedBuffer
      //     .asTypedList(data!.lengthInBytes)
      //     .setAll(0, data.buffer.asUint8List());

      // ffi.textureRendererRenderRgbaFrame(
      //   render,
      //   sharedBuffer,
      //   data.lengthInBytes,
      //   img.width,
      //   img.height,
      // );

      // if (otherRender != null) {
      //   ffi.textureRendererRenderRgbaFrame(
      //     otherRender!,
      //     sharedBuffer,
      //     data.lengthInBytes,
      //     img.width,
      //     img.height,
      //   );
      // }

      // rtmpLive.pushCustomRgbaVideo(
      //   sharedBuffer,
      //   data.lengthInBytes,
      //   img.width,
      //   img.height,
      // );
    });

    monitor.init();

    // monitor.shader = ShaderAssets.gaussianBlur;

    // recorder.init();
    // recorder.recordAudio(false);

    rtcLive.init();
    rtcLive.speak(false);

    rtmpLive.init();
    rtmpLive.speak(false);

    // rtcLive.attachRemoteAudioHandler(
    //   nativeRecorder: recorder.nativeRecorder,
    //   nativeRtmpLive: rtmpLive.nativeRtmpLive,
    // );

    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    timer?.cancel();
    monitor.dispose();
    rtcLive.attachRemoteAudioHandler();

    recorder.dispose();
    rtcLive.dispose();
    rtmpLive.dispose();

    Future.delayed(const Duration(seconds: 1), () {
      ffi.textureRendererDestroy(render);
      ffi.malloc.free(sharedBuffer);
    });
  }

  void tap() {}
}
