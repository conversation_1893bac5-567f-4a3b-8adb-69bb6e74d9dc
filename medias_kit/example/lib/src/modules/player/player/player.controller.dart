import 'dart:async';
import 'dart:ffi' as ffi;

import 'package:app_foundation/app_foundation.dart';
import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: player
///
/// 播放器
class PlayerController extends AppController with StateMixin<ApiModel> {
  PlayerController(super.key, super.routerState);

  /// Create a [Player] to control playback.
  late final player = Player();

  /// Create a [VideoController] to handle video output from [Player].
  late final controller = VideoController(player);

  late final render = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  Timer? timer;

  final sharedBuffer = ffi.malloc<ffi.Uint8>(3840 * 2160 * 4);

  var width = 0;
  var height = 0;

  @override
  void onInit() {
    player.open(
      Media('/home/<USER>/Desktop/xinanyike.mp4'),
    );
    player.stream.width.listen((width) {
      this.width = width ?? 0;
      debugPrint("width: $width");
    });
    player.stream.height.listen((height) {
      this.height = height ?? 0;
      debugPrint("height: $height");
    });
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {
    timer = Timer.periodic(const Duration(milliseconds: 40), (_) async {
      final data = await player.screenshot(format: null);
      if (data == null || width == 0 || height == 0) {
        return;
      }

      // 转换 BGRA 到 RGBA 格式并直接裁剪到 sharedBuffer
      final croppedSize = sharedBuffer.convertAndCrop(data, width, height);

      ffi.textureRendererRenderRgbaFrame(
        render,
        sharedBuffer,
        croppedSize,
        width, // 使用实际视频宽度
        height,
      );
    });
  }

  @override
  void onClose() {
    timer?.cancel();
    player.dispose();

    Future.delayed(const Duration(seconds: 1), () {
      ffi.textureRendererDestroy(render);
      ffi.malloc.free(sharedBuffer);
    });
  }
}
