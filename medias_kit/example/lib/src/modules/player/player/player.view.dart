import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:medias_kit/medias_kit.dart';

import 'player.controller.dart';

/// 所属模块: player
///
/// 播放器
class PlayerView extends AppView<PlayerController> {
  const PlayerView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('PlayerView'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Center(
      child: Stack(
        children: [
          AspectRatio(
            aspectRatio: 16 / 9,
            child: Video(
              controller: controller.controller,
            ),
          ),
          Container(
            width: 192 * 4,
            height: 108 * 4,
            color: Colors.black,
            child: Texture(
              textureId: textureRendererGetTextureId(controller.render),
            ),
          )
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
