import 'dart:ui' as ui;

import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/foundation.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: test
///
/// 音频设备测试
class TestController extends AppController with StateMixin<ApiModel> {
  TestController(super.key, super.routerState);

  late final render = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  final image = ValueNotifier<ui.Image?>(null);

  @override
  void onInit() {
    DesktopMultiWindow.setMethodHandler((method, arguments) async {
      if (method.method == "getRender") {
        return render.address;
      }
      return null;
    });

    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {}
}
