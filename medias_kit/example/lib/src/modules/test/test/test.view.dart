import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;

import 'test.controller.dart';

/// 所属模块: test
///
/// 音频设备测试
class TestView extends AppView<TestController> {
  const TestView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('TestView'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        color: Colors.black,
        child: Texture(
          textureId: ffi.textureRendererGetTextureId(controller.render),
        ),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
