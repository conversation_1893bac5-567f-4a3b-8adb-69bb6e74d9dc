import 'package:app_foundation/app_foundation.dart';
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: multi
///
/// 多路采集
class MultiController extends AppController with StateMixin<ApiModel> {
  MultiController(super.key, super.routerState);

  final monitors = HostDevice.share.videoSources.value
      .map((e) => Monitor(videoCaptureDevice: e, framerate: 30))
      .toList();

  // final rtcLiveControllers = HostDevice.share.videoSources.value.map(
  //   (e) {
  //     return RtcLive(
  //       videoCaptureDevice: e,
  //       streamType: e.isUsbExtend ? StreamType.secondary : StreamType.main,
  //       appId: "44f6c4f99b414bbaa2302b03ea08c0b1",
  //       token: e.isUsbExtend
  //           ? "007eJxTYEiaZ6maW2h56erstb94e9UVN3gsc/z4fc2JFn2FE06a8S8UGExM0sySTdIsLZNMDE2SkhITjYwNjJIMjFMTDSySDZIMFfit0xsCGRnWS2gwMTJAIIjPwZAbkJ1uWh5gwcAAAKVMHxI="
  //           : "007eJxTYIi2PVrIaZcZ9HzViV/nmeQ2MYX91Zv5k0k6Zc85XZZDIRUKDCYmaWbJJmmWlkkmhiZJSYmJRsYGRkkGxqmJBhbJBkmGsfzW6Q2BjAy/ZzcyMzJAIIjPwZAbkJ1uWh5gwcAAAHHaH1M=",
  //       channelId: "mPkg5wP8",
  //       userId: e.isUsbExtend ? "2220001" : "222",
  //     );
  //   },
  // ).toList();

  @override
  void onInit() {
    for (var element in monitors) {
      element.init();
    }
    // for (var element in rtcLiveControllers) {
    //   element.init();
    //   element.speak(true);
    // }
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    for (var element in monitors) {
      element.dispose();
    }
    // for (var element in rtcLiveControllers) {
    //   element.dispose();
    // }
  }
}
