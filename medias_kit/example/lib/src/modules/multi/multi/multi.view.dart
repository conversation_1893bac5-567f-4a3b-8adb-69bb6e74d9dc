import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:medias_kit/medias_kit.dart';

import 'multi.controller.dart';

/// 所属模块: multi
///
/// 多路采集
class MultiView extends AppView<MultiController> {
  const MultiView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('Multi Capture'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children:
          controller.monitors.map((e) => MonitorView(monitor: e)).toList(),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
