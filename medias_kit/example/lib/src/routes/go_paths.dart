import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/widgets.dart';

/// 参照 defaultSlide, 去实现不同的转场动画
class RouteTransition {
  static Widget defaultSlide(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    final tween = Tween(begin: begin, end: end);
    final offsetAnimation = animation.drive(tween);
    return SlideTransition(
      position: offsetAnimation,
      child: child,
    );
  }
}

/// 参照文档要求, 实现路由描述
@routeDetector
class GoPaths {
  @AppRoute(
    module: "home",
    stateManageType: StateManageType.static,
    description: "首页",
  )
  static const home = '/';

  @AppRoute(
    module: "next",
    stateManageType: StateManageType.static,
    description: "采集",
  )
  static const next = '/next';

  @AppRoute(
    module: "multi",
    stateManageType: StateManageType.static,
    description: "多路采集",
  )
  static const multi = '/multi';

  @AppRoute(
    module: "test",
    stateManageType: StateManageType.static,
    description: "音频设备测试",
  )
  static const test = '/test';

  @AppRoute(
    module: "player",
    stateManageType: StateManageType.static,
    description: "播放器",
  )
  static const player = '/player';
}
