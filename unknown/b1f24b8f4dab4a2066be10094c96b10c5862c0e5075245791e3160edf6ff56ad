#pragma once

#include <cstdint>
#include <memory>

#include "utils/flutter_api.hpp"

namespace MK {
class Peripheral {
  private:
    bool isInit = false;

  public:
    enum class Event {
        init,
        start,
        stop,
        notify,
        dispose
    };
    enum class ReverseEvent {
        state,
        accept
    };

    const std::shared_ptr<FlutterApi> flutterApi;

    const std::shared_ptr<const std::string> identifier;

    Peripheral(
        const std::shared_ptr<FlutterApi> flutterApi,
        const std::shared_ptr<const std::string> identifier
    );

    ~Peripheral();

    bool
    init();

    void
    start();

    void
    stop();

    void
    notify(const std::shared_ptr<const std::string> cmd) const;

    void
    dispose();
};
} // namespace MK